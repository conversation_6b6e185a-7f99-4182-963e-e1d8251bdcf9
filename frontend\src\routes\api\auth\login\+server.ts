import { json } from '@sveltejs/kit'
import type { RequestHand<PERSON> } from './$types'

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { email, password } = await request.json()

    // Validate input
    if (!email || !password) {
      return json(
        { message: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Call Medusa.js auth endpoint
    const medusaResponse = await fetch(`${process.env.MEDUSA_BACKEND_URL}/store/auth`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    })

    const medusaData = await medusaResponse.json()

    if (!medusaResponse.ok) {
      return json(
        { message: medusaData.message || 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Get customer data
    const customerResponse = await fetch(`${process.env.MEDUSA_BACKEND_URL}/store/customers/me`, {
      headers: {
        'Authorization': `Bearer ${medusaData.access_token}`,
      },
    })

    if (!customerResponse.ok) {
      return json(
        { message: 'Failed to get customer data' },
        { status: 500 }
      )
    }

    const customerData = await customerResponse.json()

    return json({
      success: true,
      token: medusaData.access_token,
      customer: customerData.customer,
      message: 'Login successful'
    })

  } catch (error) {
    console.error('Login error:', error)
    return json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
