import { writable } from 'svelte/store'
import type { Cart, CartItem, Product } from '$lib/types/products'

// Cart store
export const cartStore = writable<Cart>({
  items: [],
  total_amount: 0,
  currency: 'USD'
})

// Cart functions
export const cartActions = {
  // Add item to cart
  addItem: (product: Product, quantity: number = 1) => {
    cartStore.update(cart => {
      const existingItemIndex = cart.items.findIndex(item => item.product_id === product.id)
      
      if (existingItemIndex >= 0) {
        // Update quantity if item already exists
        cart.items[existingItemIndex].quantity += quantity
      } else {
        // Add new item
        const newItem: CartItem = {
          product_id: product.id,
          product,
          quantity,
          added_at: new Date().toISOString()
        }
        cart.items.push(newItem)
      }
      
      // Recalculate total
      cart.total_amount = cart.items.reduce((total, item) => {
        return total + (item.product.price * item.quantity)
      }, 0)
      
      // Apply discount if exists
      if (cart.discount_amount) {
        cart.total_amount -= cart.discount_amount
      }
      
      return cart
    })
    
    // Save to localStorage
    saveCartToStorage()
  },

  // Remove item from cart
  removeItem: (productId: string) => {
    cartStore.update(cart => {
      cart.items = cart.items.filter(item => item.product_id !== productId)
      
      // Recalculate total
      cart.total_amount = cart.items.reduce((total, item) => {
        return total + (item.product.price * item.quantity)
      }, 0)
      
      // Apply discount if exists
      if (cart.discount_amount) {
        cart.total_amount -= cart.discount_amount
      }
      
      return cart
    })
    
    saveCartToStorage()
  },

  // Update item quantity
  updateQuantity: (productId: string, quantity: number) => {
    if (quantity <= 0) {
      cartActions.removeItem(productId)
      return
    }
    
    cartStore.update(cart => {
      const itemIndex = cart.items.findIndex(item => item.product_id === productId)
      
      if (itemIndex >= 0) {
        cart.items[itemIndex].quantity = quantity
        
        // Recalculate total
        cart.total_amount = cart.items.reduce((total, item) => {
          return total + (item.product.price * item.quantity)
        }, 0)
        
        // Apply discount if exists
        if (cart.discount_amount) {
          cart.total_amount -= cart.discount_amount
        }
      }
      
      return cart
    })
    
    saveCartToStorage()
  },

  // Clear cart
  clearCart: () => {
    cartStore.set({
      items: [],
      total_amount: 0,
      currency: 'USD'
    })
    
    saveCartToStorage()
  },

  // Apply discount code
  applyDiscount: (code: string, amount: number) => {
    cartStore.update(cart => {
      cart.discount_code = code
      cart.discount_amount = amount
      
      // Recalculate total with discount
      const subtotal = cart.items.reduce((total, item) => {
        return total + (item.product.price * item.quantity)
      }, 0)
      
      cart.total_amount = Math.max(0, subtotal - amount)
      
      return cart
    })
    
    saveCartToStorage()
  },

  // Remove discount
  removeDiscount: () => {
    cartStore.update(cart => {
      cart.discount_code = undefined
      cart.discount_amount = undefined
      
      // Recalculate total without discount
      cart.total_amount = cart.items.reduce((total, item) => {
        return total + (item.product.price * item.quantity)
      }, 0)
      
      return cart
    })
    
    saveCartToStorage()
  }
}

// Helper functions
function saveCartToStorage() {
  if (typeof localStorage !== 'undefined') {
    cartStore.subscribe(cart => {
      localStorage.setItem('hebrew_cart', JSON.stringify(cart))
    })()
  }
}

export function loadCartFromStorage() {
  if (typeof localStorage !== 'undefined') {
    const savedCart = localStorage.getItem('hebrew_cart')
    if (savedCart) {
      try {
        const cart = JSON.parse(savedCart) as Cart
        cartStore.set(cart)
      } catch (e) {
        console.error('Failed to load cart from storage:', e)
      }
    }
  }
}

// Derived stores
export const cartItemCount = writable(0)
export const cartTotal = writable(0)

cartStore.subscribe(cart => {
  cartItemCount.set(cart.items.reduce((total, item) => total + item.quantity, 0))
  cartTotal.set(cart.total_amount)
})
