import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { BOOK_MODULE } from "../../../modules/book"

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const bookService = req.scope.resolve(BOOK_MODULE)
  
  try {
    const {
      limit = 20,
      offset = 0,
      difficulty_level,
      language = "he",
      is_published = true,
      is_free,
      order = "order_in_book"
    } = req.query

    const filters: any = {
      is_published,
      language
    }

    if (difficulty_level) {
      filters.difficulty_level = difficulty_level
    }

    if (is_free !== undefined) {
      filters.is_free = is_free === "true"
    }

    const options = {
      skip: parseInt(offset as string),
      take: parseInt(limit as string),
      order: { [order as string]: "ASC" }
    }

    const [chapters, count] = await bookService.listChapters(filters, options)
    
    // Return only safe fields for public API
    const safeChapters = chapters.map(chapter => ({
      id: chapter.id,
      title: chapter.title,
      preview_content: chapter.preview_content,
      difficulty_level: chapter.difficulty_level,
      order_in_book: chapter.order_in_book,
      language: chapter.language,
      price: chapter.price,
      is_free: chapter.is_free,
      reading_time_minutes: chapter.reading_time_minutes,
      tags: chapter.tags ? JSON.parse(chapter.tags) : [],
      audio_url: chapter.audio_url,
      video_url: chapter.video_url,
      created_at: chapter.created_at
    }))

    res.json({
      chapters: safeChapters,
      count,
      offset: parseInt(offset as string),
      limit: parseInt(limit as string)
    })
  } catch (error) {
    console.error("Error fetching chapters:", error)
    res.status(500).json({ 
      message: "Internal server error",
      error: process.env.NODE_ENV === "development" ? error.message : undefined
    })
  }
}
