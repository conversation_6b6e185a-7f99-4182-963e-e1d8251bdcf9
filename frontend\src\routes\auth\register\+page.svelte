<script lang="ts">
	import { onMount } from 'svelte'
	import { goto } from '$app/navigation'
	import { _ } from '$lib/i18n'
	import { authActions, authStore, isAuthenticated } from '$lib/stores/auth'
	import { getCurrentLocale, isRTL } from '$lib/i18n'

	let currentLocale = 'en'
	let rtl = false
	let formData = {
		email: '',
		password: '',
		confirmPassword: '',
		first_name: '',
		last_name: '',
		phone: ''
	}
	let showPassword = false
	let showConfirmPassword = false
	let isLoading = false
	let errors: Record<string, string> = {}

	$: currentLocale = getCurrentLocale()
	$: rtl = isRTL(currentLocale)
	$: authState = $authStore
	$: isAuth = $isAuthenticated

	// Redirect if already authenticated
	onMount(() => {
		if (isAuth) {
			goto('/profile')
		}
	})

	// Watch for authentication changes
	$: if (isAuth) {
		goto('/profile')
	}

	function validateForm() {
		errors = {}

		if (!formData.first_name.trim()) {
			errors.first_name = $_('auth.first_name_required')
		}

		if (!formData.last_name.trim()) {
			errors.last_name = $_('auth.last_name_required')
		}

		if (!formData.email.trim()) {
			errors.email = $_('auth.email_required')
		} else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
			errors.email = $_('auth.email_invalid')
		}

		if (!formData.password) {
			errors.password = $_('auth.password_required')
		} else if (formData.password.length < 8) {
			errors.password = $_('auth.password_min_length')
		}

		if (!formData.confirmPassword) {
			errors.confirmPassword = $_('auth.confirm_password_required')
		} else if (formData.password !== formData.confirmPassword) {
			errors.confirmPassword = $_('auth.passwords_dont_match')
		}

		return Object.keys(errors).length === 0
	}

	async function handleRegister() {
		if (!validateForm()) {
			return
		}

		isLoading = true

		const result = await authActions.register({
			email: formData.email,
			password: formData.password,
			first_name: formData.first_name,
			last_name: formData.last_name,
			phone: formData.phone || undefined
		})

		if (result.success) {
			// Redirect will happen automatically via reactive statement
		} else {
			errors.general = result.error || $_('auth.registration_failed')
		}

		isLoading = false
	}

	function togglePasswordVisibility(field: 'password' | 'confirmPassword') {
		if (field === 'password') {
			showPassword = !showPassword
		} else {
			showConfirmPassword = !showConfirmPassword
		}
	}
</script>

<svelte:head>
	<title>{$_('auth.register')} - Hebrew Book Store</title>
	<meta name="description" content="Create your Hebrew Book Store account" />
</svelte:head>

<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8" style="background: var(--color-bg-primary);" class:rtl>
	<div class="max-w-md w-full space-y-8">
		<!-- Header -->
		<div class="text-center">
			<h2 class="mt-6 text-3xl font-bold" style="color: var(--color-text-primary);">
				{$_('auth.create_account')}
			</h2>
			<p class="mt-2 text-sm" style="color: var(--color-text-secondary);">
				{$_('auth.register_subtitle')}
			</p>
		</div>

		<!-- Registration Form -->
		<form class="mt-8 space-y-6" on:submit|preventDefault={handleRegister}>
			<div class="space-y-4">
				<!-- Name Fields -->
				<div class="grid grid-cols-2 gap-4">
					<!-- First Name -->
					<div>
						<label for="first_name" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary);">
							{$_('auth.first_name')}
						</label>
						<input
							id="first_name"
							name="first_name"
							type="text"
							autocomplete="given-name"
							required
							bind:value={formData.first_name}
							placeholder={$_('auth.first_name_placeholder')}
							class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
							class:border-red-300={errors.first_name}
							class:border-gray-300={!errors.first_name}
							style="background: var(--color-bg-primary);"
							disabled={isLoading}
						/>
						{#if errors.first_name}
							<p class="mt-1 text-sm text-red-600">{errors.first_name}</p>
						{/if}
					</div>

					<!-- Last Name -->
					<div>
						<label for="last_name" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary);">
							{$_('auth.last_name')}
						</label>
						<input
							id="last_name"
							name="last_name"
							type="text"
							autocomplete="family-name"
							required
							bind:value={formData.last_name}
							placeholder={$_('auth.last_name_placeholder')}
							class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
							class:border-red-300={errors.last_name}
							class:border-gray-300={!errors.last_name}
							style="background: var(--color-bg-primary);"
							disabled={isLoading}
						/>
						{#if errors.last_name}
							<p class="mt-1 text-sm text-red-600">{errors.last_name}</p>
						{/if}
					</div>
				</div>

				<!-- Email -->
				<div>
					<label for="email" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary);">
						{$_('auth.email')}
					</label>
					<input
						id="email"
						name="email"
						type="email"
						autocomplete="email"
						required
						bind:value={formData.email}
						placeholder={$_('auth.email_placeholder')}
						class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
						class:border-red-300={errors.email}
						class:border-gray-300={!errors.email}
						style="background: var(--color-bg-primary);"
						disabled={isLoading}
					/>
					{#if errors.email}
						<p class="mt-1 text-sm text-red-600">{errors.email}</p>
					{/if}
				</div>

				<!-- Password -->
				<div>
					<label for="password" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary);">
						{$_('auth.password')}
					</label>
					<div class="relative">
						<input
							id="password"
							name="password"
							type={showPassword ? 'text' : 'password'}
							autocomplete="new-password"
							required
							bind:value={formData.password}
							placeholder={$_('auth.password_placeholder')}
							class="w-full px-3 py-2 pr-10 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
							class:border-red-300={errors.password}
							class:border-gray-300={!errors.password}
							style="background: var(--color-bg-primary);"
							disabled={isLoading}
						/>
						<button
							type="button"
							class="absolute inset-y-0 right-0 pr-3 flex items-center"
							on:click={() => togglePasswordVisibility('password')}
						>
							{#if showPassword}
								<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
								</svg>
							{:else}
								<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
								</svg>
							{/if}
						</button>
					</div>
					{#if errors.password}
						<p class="mt-1 text-sm text-red-600">{errors.password}</p>
					{/if}
				</div>

				<!-- Confirm Password -->
				<div>
					<label for="confirmPassword" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary);">
						{$_('auth.confirm_password')}
					</label>
					<div class="relative">
						<input
							id="confirmPassword"
							name="confirmPassword"
							type={showConfirmPassword ? 'text' : 'password'}
							autocomplete="new-password"
							required
							bind:value={formData.confirmPassword}
							placeholder={$_('auth.confirm_password_placeholder')}
							class="w-full px-3 py-2 pr-10 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
							class:border-red-300={errors.confirmPassword}
							class:border-gray-300={!errors.confirmPassword}
							style="background: var(--color-bg-primary);"
							disabled={isLoading}
						/>
						<button
							type="button"
							class="absolute inset-y-0 right-0 pr-3 flex items-center"
							on:click={() => togglePasswordVisibility('confirmPassword')}
						>
							{#if showConfirmPassword}
								<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
								</svg>
							{:else}
								<svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
								</svg>
							{/if}
						</button>
					</div>
					{#if errors.confirmPassword}
						<p class="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>
					{/if}
				</div>
			</div>

			<!-- General Error Message -->
			{#if errors.general}
				<div class="bg-red-50 border border-red-200 rounded-lg p-3">
					<p class="text-sm text-red-600">{errors.general}</p>
				</div>
			{/if}

			<!-- Submit Button -->
			<div>
				<button
					type="submit"
					disabled={isLoading}
					class="w-full flex justify-center py-2 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
				>
					{#if isLoading}
						<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
							<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
							<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
						</svg>
						{$_('auth.creating_account')}
					{:else}
						{$_('auth.create_account')}
					{/if}
				</button>
			</div>

			<!-- Links -->
			<div class="text-center">
				<p class="text-sm" style="color: var(--color-text-secondary);">
					{$_('auth.already_have_account')} 
					<a href="/auth/login" class="font-medium text-blue-600 hover:text-blue-500">
						{$_('auth.login')}
					</a>
				</p>
			</div>
		</form>
	</div>
</div>

<style>
	.rtl {
		direction: rtl;
	}
</style>
