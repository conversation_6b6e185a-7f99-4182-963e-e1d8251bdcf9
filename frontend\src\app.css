@tailwind base;
@tailwind components;
@tailwind utilities;

/* Hebrew Book Store - Elegant Classic Design Theme */
:root {
  /* Primary Colors - Muted and elegant */
  --color-primary: #B8860B; /* Muted dark goldenrod */
  --color-primary-light: #DAA520; /* Softer goldenrod */
  --color-primary-dark: #8B6914; /* Darker muted gold */

  /* Secondary Colors */
  --color-secondary: #6B5B73; /* Muted purple-brown */
  --color-secondary-light: #8B7D8B; /* Light muted purple */
  --color-secondary-dark: #4A4458; /* Dark muted purple */

  /* Background Colors - White as primary */
  --color-bg-primary: #FFFFFF; /* Pure white */
  --color-bg-secondary: #FEFEFE; /* Off-white */
  --color-bg-accent: #F8F6F0; /* Very light warm cream */
  --color-bg-subtle: #F5F5F0; /* Subtle warm gray */

  /* Text Colors */
  --color-text-primary: #2D2D2D; /* Soft black */
  --color-text-secondary: #5A5A5A; /* Medium gray */
  --color-text-accent: #6B5B73; /* Muted purple accent */
  --color-text-light: #8A8A8A; /* Light gray */

  /* Border Colors */
  --color-border: #D4C5B9; /* Soft beige border */
  --color-border-light: #E8E0D6; /* Very light beige */
  --color-border-accent: #B8860B; /* Accent border */
}

/* Custom Typography */
@import url('https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;0,700;1,400&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');

.font-book-title {
  font-family: 'Playfair Display', serif;
}

.font-book-text {
  font-family: 'Crimson Text', serif;
}

/* Decorative Elements */
.decorative-border {
  border: 1px solid var(--color-border);
  border-radius: 6px;
  position: relative;
}

.decorative-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px solid var(--color-border-light);
  border-radius: 8px;
  z-index: -1;
}

/* Classic Button Style */
.btn-classic {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: white;
  border: 1px solid var(--color-primary-dark);
  border-radius: 4px;
  padding: 12px 24px;
  font-family: 'Crimson Text', serif;
  font-weight: 600;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.btn-classic:hover {
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

/* Card Styles */
.card-classic {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-light);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
}

.card-classic::before {
  content: '';
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  border: 1px solid var(--color-border-light);
  border-radius: 6px;
  pointer-events: none;
  opacity: 0.5;
}

@layer base {
  html {
    font-family: system-ui, sans-serif;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700;
  }
  
  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }
}
