import { json } from '@sveltejs/kit'
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types'
import { MEDUSA_BACKEND_URL } from '$env/static/private'

export const POST: RequestHandler = async ({ request, fetch }) => {
  try {
    const authHeader = request.headers.get('authorization')
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      
      // Call Medusa.js logout endpoint if available
      try {
        await fetch(`${MEDUSA_BACKEND_URL}/store/auth`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        })
      } catch (error) {
        // Ignore errors from logout endpoint
        console.log('Logout endpoint error (ignored):', error)
      }
    }

    return json({
      success: true,
      message: 'Logout successful'
    })

  } catch (error) {
    console.error('Logout error:', error)
    return json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
