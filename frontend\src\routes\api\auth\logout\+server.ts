import { json } from '@sveltejs/kit'
import type { RequestHand<PERSON> } from './$types'

export const POST: RequestHandler = async ({ request }) => {
  try {
    const authHeader = request.headers.get('authorization')
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      
      // Call Medusa.js logout endpoint if available
      try {
        await fetch(`${process.env.MEDUSA_BACKEND_URL}/store/auth`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        })
      } catch (error) {
        // Ignore errors from logout endpoint
        console.log('Logout endpoint error (ignored):', error)
      }
    }

    return json({
      success: true,
      message: 'Logout successful'
    })

  } catch (error) {
    console.error('Logout error:', error)
    return json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
