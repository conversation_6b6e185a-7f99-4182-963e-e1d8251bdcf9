#!/bin/bash

# Hebrew Book Store - Test Environment Setup Script
# This script automatically sets up the test environment with API keys and users

set -e  # Exit on any error

echo "🚀 Hebrew Book Store - Test Environment Setup"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
POSTGRES_CONTAINER="hebrew-book-store-postgres"
POSTGRES_USER="medusa_user"
POSTGRES_DB="hebrew_book_clean"
MEDUSA_PORT="9000"
FRONTEND_PORT="5173"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    print_status "Checking Docker status..."
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Check if PostgreSQL container is running
check_postgres() {
    print_status "Checking PostgreSQL container..."
    if ! docker ps | grep -q $POSTGRES_CONTAINER; then
        print_error "PostgreSQL container '$POSTGRES_CONTAINER' is not running."
        print_status "Starting PostgreSQL container..."
        docker start $POSTGRES_CONTAINER || {
            print_error "Failed to start PostgreSQL container"
            exit 1
        }
        sleep 5
    fi
    print_success "PostgreSQL container is running"
}

# Check if Medusa.js is running
check_medusa() {
    print_status "Checking Medusa.js server..."
    if curl -s "http://localhost:$MEDUSA_PORT/health" > /dev/null; then
        print_success "Medusa.js server is running on port $MEDUSA_PORT"
        return 0
    else
        print_warning "Medusa.js server is not running on port $MEDUSA_PORT"
        print_status "Please start Medusa.js server with: cd backend && yarn dev"
        return 1
    fi
}

# Initialize test data in PostgreSQL
init_test_data() {
    print_status "Initializing test data in PostgreSQL..."
    
    # Check if SQL script exists
    if [ ! -f "scripts/init-test-data.sql" ]; then
        print_error "SQL script not found: scripts/init-test-data.sql"
        exit 1
    fi
    
    # Execute SQL script
    docker exec -i $POSTGRES_CONTAINER psql -U $POSTGRES_USER -d $POSTGRES_DB < scripts/init-test-data.sql
    
    if [ $? -eq 0 ]; then
        print_success "Test data initialized successfully"
    else
        print_error "Failed to initialize test data"
        exit 1
    fi
}

# Verify API keys
verify_api_keys() {
    print_status "Verifying API keys..."
    
    # Check publishable key
    PUBLISHABLE_KEY=$(docker exec $POSTGRES_CONTAINER psql -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SELECT token FROM api_key WHERE type='publishable' AND id='pk_test_hebrew_book_dev';" | xargs)
    
    if [ "$PUBLISHABLE_KEY" = "pk_test_hebrew_book_store_development" ]; then
        print_success "Publishable API key verified: $PUBLISHABLE_KEY"
    else
        print_error "Publishable API key not found or incorrect"
        exit 1
    fi
}

# Test API endpoints
test_api_endpoints() {
    print_status "Testing API endpoints..."
    
    # Test health endpoint
    if curl -s "http://localhost:$MEDUSA_PORT/health" | grep -q "OK"; then
        print_success "Health endpoint working"
    else
        print_warning "Health endpoint not responding"
    fi
    
    # Test store products endpoint with publishable key
    if curl -s -H "x-publishable-api-key: pk_test_hebrew_book_store_development" "http://localhost:$MEDUSA_PORT/store/products" | grep -q "products"; then
        print_success "Store products endpoint working with API key"
    else
        print_warning "Store products endpoint not working (this is expected if no products exist yet)"
    fi
}

# Update frontend environment
update_frontend_env() {
    print_status "Updating frontend environment..."
    
    # Update .env file with correct API key
    if [ -f "frontend/.env" ]; then
        sed -i 's/VITE_MEDUSA_PUBLISHABLE_API_KEY=.*/VITE_MEDUSA_PUBLISHABLE_API_KEY=pk_test_hebrew_book_store_development/' frontend/.env
        print_success "Frontend .env updated with correct API key"
    else
        print_warning "Frontend .env file not found"
    fi
}

# Display setup summary
display_summary() {
    echo ""
    echo "🎉 Setup Complete!"
    echo "=================="
    echo ""
    echo -e "${GREEN}✅ PostgreSQL:${NC} Running with test data"
    echo -e "${GREEN}✅ API Keys:${NC} Created and verified"
    echo -e "${GREEN}✅ Test User:${NC} <EMAIL> / admin123"
    echo ""
    echo "🔗 URLs:"
    echo "  • Frontend: http://localhost:$FRONTEND_PORT"
    echo "  • Admin Login: http://localhost:$FRONTEND_PORT/admin/login"
    echo "  • Catalog: http://localhost:$FRONTEND_PORT/chapters"
    echo "  • Medusa API: http://localhost:$MEDUSA_PORT"
    echo ""
    echo "🔑 API Keys:"
    echo "  • Publishable: pk_test_hebrew_book_store_development"
    echo "  • Secret: sk_test_hebrew_book_store_development_secret"
    echo ""
    echo "👤 Test Credentials:"
    echo "  • Email: <EMAIL>"
    echo "  • Password: admin123"
    echo ""
    echo "🚀 Next Steps:"
    echo "  1. Start frontend: cd frontend && yarn dev"
    echo "  2. Open browser: http://localhost:$FRONTEND_PORT/admin/login"
    echo "  3. Login with test credentials"
    echo "  4. Test API integration!"
}

# Main execution
main() {
    echo ""
    check_docker
    check_postgres
    
    # Check if Medusa is running (optional)
    MEDUSA_RUNNING=false
    if check_medusa; then
        MEDUSA_RUNNING=true
    fi
    
    init_test_data
    verify_api_keys
    update_frontend_env
    
    if [ "$MEDUSA_RUNNING" = true ]; then
        test_api_endpoints
    fi
    
    display_summary
}

# Run main function
main
