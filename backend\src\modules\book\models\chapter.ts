import { model } from "@medusajs/framework/utils"

const Chapter = model.define("chapter", {
  id: model.id().primary<PERSON>ey(),
  title: model.text(),
  content: model.text(),
  preview_content: model.text(),
  difficulty_level: model.text(), // "beginner", "intermediate", "advanced"
  order_in_book: model.number(),
  is_published: model.boolean().default(false),
  language: model.text().default("he"), // "he" for Hebrew, "ru" for Russian, "en" for English
  price: model.number().nullable(), // Price for individual chapter purchase
  is_free: model.boolean().default(false), // Free chapters for preview
  audio_url: model.text().nullable(), // URL to audio file if available
  video_url: model.text().nullable(), // URL to video file if available
  reading_time_minutes: model.number().nullable(), // Estimated reading time
  tags: model.text().nullable(), // JSON string of tags for categorization
})

export default Chapter
