import { writable } from 'svelte/store'
import { browser } from '$app/environment'
import { goto } from '$app/navigation'
import medusaClient from '$lib/api/medusa-client'
import { env } from '$env/dynamic/public'

export interface User {
  id: string
  email: string
  first_name: string
  last_name: string
  phone?: string
  billing_address?: any
  shipping_address?: any
  created_at: string
  updated_at: string
  has_account: boolean
}

// Helper function to convert Medusa customer to User
function customerToUser(customer: any): User {
  return {
    id: customer.id,
    email: customer.email,
    first_name: customer.first_name || '',
    last_name: customer.last_name || '',
    phone: customer.phone || undefined,
    billing_address: customer.billing_address,
    shipping_address: customer.shipping_address,
    has_account: true,
    created_at: customer.created_at ? new Date(customer.created_at).toISOString() : new Date().toISOString(),
    updated_at: customer.updated_at ? new Date(customer.updated_at).toISOString() : new Date().toISOString()
  }
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

// Initial state
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null
}

// Create stores
export const authStore = writable<AuthState>(initialState)
export const isAuthenticated = writable<boolean>(false)
export const currentUser = writable<User | null>(null)

// Auth actions
export const authActions = {
  // Set loading state
  setLoading: (loading: boolean) => {
    authStore.update(state => ({ ...state, isLoading: loading }))
  },

  // Set error
  setError: (error: string | null) => {
    authStore.update(state => ({ ...state, error }))
  },

  // Login
  login: async (email: string, password: string) => {
    authActions.setLoading(true)
    authActions.setError(null)

    try {
      // Use Medusa SDK for authentication
      await medusaClient.auth.login("customer", "emailpass", { email, password })

      // Get customer data
      const { customer } = await medusaClient.store.customer.retrieve()
      const user = customerToUser(customer)

      // Update auth state
      authStore.update(state => ({
        ...state,
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null
      }))

      isAuthenticated.set(true)
      currentUser.set(user)

      return { success: true, user }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed'
      authActions.setError(errorMessage)
      authActions.setLoading(false)
      return { success: false, error: errorMessage }
    }
  },

  // Register - Using Medusa SDK
  register: async (userData: {
    email: string
    password: string
    first_name: string
    last_name: string
    phone?: string
  }) => {
    authActions.setLoading(true)
    authActions.setError(null)

    try {
      // Step 1: Register with Medusa SDK
      await medusaClient.auth.register("customer", "emailpass", {
        email: userData.email,
        password: userData.password
      })

      // Step 2: Create customer
      const { customer } = await medusaClient.store.customer.create({
        email: userData.email,
        first_name: userData.first_name,
        last_name: userData.last_name,
        phone: userData.phone
      })

      // Update auth state
      const user = customerToUser(customer)

      authStore.update(state => ({
        ...state,
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null
      }))

      isAuthenticated.set(true)
      currentUser.set(user)

      return { success: true, user }
    } catch (error) {
      // If registration fails, try login (user might already exist)
      try {
        console.log('Registration failed, trying login for existing user')

        await medusaClient.auth.login("customer", "emailpass", {
          email: userData.email,
          password: userData.password
        })

        // Create customer after login
        const { customer } = await medusaClient.store.customer.create({
          email: userData.email,
          first_name: userData.first_name,
          last_name: userData.last_name,
          phone: userData.phone
        })

        // Update auth state
        const user = customerToUser(customer)

        authStore.update(state => ({
          ...state,
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null
        }))

        isAuthenticated.set(true)
        currentUser.set(user)

        return { success: true, user }
      } catch (loginError) {
        const errorMessage = error instanceof Error ? error.message : 'Registration failed'
        authActions.setError(errorMessage)
        authActions.setLoading(false)
        return { success: false, error: errorMessage }
      }
    }
  },

  // Logout
  logout: async () => {
    try {
      // Call Medusa.js logout endpoint if available
      await medusaClient.auth.logout()
    } catch (error) {
      console.error('Logout error:', error)
    }

    // Reset auth state
    authStore.set(initialState)
    isAuthenticated.set(false)
    currentUser.set(null)

    // Redirect to home
    goto('/')
  },

  // Check authentication status
  checkAuth: async () => {
    const token = getAuthToken()
    if (!token) {
      return false
    }

    authActions.setLoading(true)

    try {
      // Get customer data from Medusa.js
      const { customer } = await medusaClient.store.customer.retrieve()
      const user = customerToUser(customer)

      // Update auth state
      authStore.update(state => ({
        ...state,
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null
      }))

      isAuthenticated.set(true)
      currentUser.set(user)

      return true
    } catch (error) {
      // Clear auth state
      authStore.set({ ...initialState, isLoading: false })
      isAuthenticated.set(false)
      currentUser.set(null)

      return false
    }
  },

  // Forgot password
  forgotPassword: async (email: string) => {
    authActions.setLoading(true)
    authActions.setError(null)

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to send reset email')
      }

      authActions.setLoading(false)
      return { success: true, message: data.message }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send reset email'
      authActions.setError(errorMessage)
      authActions.setLoading(false)
      return { success: false, error: errorMessage }
    }
  },

  // Reset password
  resetPassword: async (token: string, password: string) => {
    authActions.setLoading(true)
    authActions.setError(null)

    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token, password }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to reset password')
      }

      authActions.setLoading(false)
      return { success: true, message: data.message }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to reset password'
      authActions.setError(errorMessage)
      authActions.setLoading(false)
      return { success: false, error: errorMessage }
    }
  }
}

// Helper function to get auth token
export function getAuthToken(): string | null {
  if (!browser) return null
  return localStorage.getItem('auth_token')
}

// Helper function to get auth headers
export function getAuthHeaders(): Record<string, string> {
  const token = getAuthToken()
  return token ? { 'Authorization': `Bearer ${token}` } : {}
}

// Initialize auth state on app load
if (browser) {
  authActions.checkAuth()
}
