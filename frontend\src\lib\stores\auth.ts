import { writable } from 'svelte/store'
import { browser } from '$app/environment'
import { goto } from '$app/navigation'
import { env } from '$env/dynamic/public'

export interface User {
  id: string
  email: string
  first_name: string
  last_name: string
  phone?: string
  billing_address?: any
  shipping_address?: any
  created_at: string
  updated_at: string
  has_account: boolean
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

// Initial state
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null
}

// Create stores
export const authStore = writable<AuthState>(initialState)
export const isAuthenticated = writable<boolean>(false)
export const currentUser = writable<User | null>(null)

// Auth actions
export const authActions = {
  // Set loading state
  setLoading: (loading: boolean) => {
    authStore.update(state => ({ ...state, isLoading: loading }))
  },

  // Set error
  setError: (error: string | null) => {
    authStore.update(state => ({ ...state, error }))
  },

  // Login
  login: async (email: string, password: string) => {
    authActions.setLoading(true)
    authActions.setError(null)

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Login failed')
      }

      // Store JWT token
      if (browser) {
        localStorage.setItem('auth_token', data.token)
      }

      // Update auth state
      authStore.update(state => ({
        ...state,
        user: data.customer,
        isAuthenticated: true,
        isLoading: false,
        error: null
      }))

      isAuthenticated.set(true)
      currentUser.set(data.customer)

      return { success: true, user: data.customer }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed'
      authActions.setError(errorMessage)
      authActions.setLoading(false)
      return { success: false, error: errorMessage }
    }
  },

  // Register
  register: async (userData: {
    email: string
    password: string
    first_name: string
    last_name: string
    phone?: string
  }) => {
    authActions.setLoading(true)
    authActions.setError(null)

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Registration failed')
      }

      // Store JWT token
      if (browser) {
        localStorage.setItem('auth_token', data.token)
      }

      // Update auth state
      authStore.update(state => ({
        ...state,
        user: data.customer,
        isAuthenticated: true,
        isLoading: false,
        error: null
      }))

      isAuthenticated.set(true)
      currentUser.set(data.customer)

      return { success: true, user: data.customer }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Registration failed'
      authActions.setError(errorMessage)
      authActions.setLoading(false)
      return { success: false, error: errorMessage }
    }
  },

  // Logout
  logout: async () => {
    try {
      // Call logout endpoint
      await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
        },
      })
    } catch (error) {
      console.error('Logout error:', error)
    }

    // Clear local storage
    if (browser) {
      localStorage.removeItem('auth_token')
    }

    // Reset auth state
    authStore.set(initialState)
    isAuthenticated.set(false)
    currentUser.set(null)

    // Redirect to home
    goto('/')
  },

  // Check authentication status
  checkAuth: async () => {
    const token = getAuthToken()
    if (!token) {
      return false
    }

    authActions.setLoading(true)

    try {
      const response = await fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (!response.ok) {
        throw new Error('Authentication check failed')
      }

      const data = await response.json()

      // Update auth state
      authStore.update(state => ({
        ...state,
        user: data.customer,
        isAuthenticated: true,
        isLoading: false,
        error: null
      }))

      isAuthenticated.set(true)
      currentUser.set(data.customer)

      return true
    } catch (error) {
      // Clear invalid token
      if (browser) {
        localStorage.removeItem('auth_token')
      }
      
      authStore.set({ ...initialState, isLoading: false })
      isAuthenticated.set(false)
      currentUser.set(null)
      
      return false
    }
  },

  // Forgot password
  forgotPassword: async (email: string) => {
    authActions.setLoading(true)
    authActions.setError(null)

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to send reset email')
      }

      authActions.setLoading(false)
      return { success: true, message: data.message }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send reset email'
      authActions.setError(errorMessage)
      authActions.setLoading(false)
      return { success: false, error: errorMessage }
    }
  },

  // Reset password
  resetPassword: async (token: string, password: string) => {
    authActions.setLoading(true)
    authActions.setError(null)

    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token, password }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to reset password')
      }

      authActions.setLoading(false)
      return { success: true, message: data.message }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to reset password'
      authActions.setError(errorMessage)
      authActions.setLoading(false)
      return { success: false, error: errorMessage }
    }
  }
}

// Helper function to get auth token
export function getAuthToken(): string | null {
  if (!browser) return null
  return localStorage.getItem('auth_token')
}

// Helper function to get auth headers
export function getAuthHeaders(): Record<string, string> {
  const token = getAuthToken()
  return token ? { 'Authorization': `Bearer ${token}` } : {}
}

// Initialize auth state on app load
if (browser) {
  authActions.checkAuth()
}
