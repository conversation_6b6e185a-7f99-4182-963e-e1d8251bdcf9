import { writable } from 'svelte/store'
import { browser } from '$app/environment'
import { goto } from '$app/navigation'
import { apiClient } from '$lib/api/client'
import { env } from '$env/dynamic/public'

export interface User {
  id: string
  email: string
  first_name: string
  last_name: string
  phone?: string
  billing_address?: any
  shipping_address?: any
  created_at: string
  updated_at: string
  has_account: boolean
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

// Initial state
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null
}

// Create stores
export const authStore = writable<AuthState>(initialState)
export const isAuthenticated = writable<boolean>(false)
export const currentUser = writable<User | null>(null)

// Auth actions
export const authActions = {
  // Set loading state
  setLoading: (loading: boolean) => {
    authStore.update(state => ({ ...state, isLoading: loading }))
  },

  // Set error
  setError: (error: string | null) => {
    authStore.update(state => ({ ...state, error }))
  },

  // Login
  login: async (email: string, password: string) => {
    authActions.setLoading(true)
    authActions.setError(null)

    try {
      // Call Medusa.js auth endpoint directly
      const authResponse = await apiClient.authenticate({ email, password })

      if (authResponse.error || !authResponse.data) {
        throw new Error(authResponse.error || 'Invalid credentials')
      }

      const token = authResponse.data.token || authResponse.data.access_token

      if (!token) {
        throw new Error('No authentication token received')
      }

      // Store JWT token
      apiClient.setAuthToken(token)

      // Get customer data
      const customerResponse = await apiClient.getCustomer()

      if (customerResponse.error || !customerResponse.data?.customer) {
        throw new Error('Failed to get customer data')
      }

      const customer = customerResponse.data.customer

      // Update auth state
      authStore.update(state => ({
        ...state,
        user: customer,
        isAuthenticated: true,
        isLoading: false,
        error: null
      }))

      isAuthenticated.set(true)
      currentUser.set(customer)

      return { success: true, user: customer }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed'
      authActions.setError(errorMessage)
      authActions.setLoading(false)
      return { success: false, error: errorMessage }
    }
  },

  // Register - Following Medusa.js v2 documentation
  register: async (userData: {
    email: string
    password: string
    first_name: string
    last_name: string
    phone?: string
  }) => {
    authActions.setLoading(true)
    authActions.setError(null)

    try {
      let authToken: string

      try {
        // Step 1: Try to get registration token
        const registrationResponse = await apiClient.getRegistrationToken({
          email: userData.email,
          password: userData.password
        })

        if (registrationResponse.error) {
          throw new Error(registrationResponse.error)
        }

        authToken = registrationResponse.data?.token || ''
      } catch (registrationError) {
        // If registration fails (email already exists), try to login
        console.log('Registration failed, trying login for existing user')

        const loginResponse = await apiClient.authenticate({
          email: userData.email,
          password: userData.password
        })

        if (loginResponse.error || !loginResponse.data) {
          throw new Error('Email already exists with different password or registration failed')
        }

        authToken = loginResponse.data.token || loginResponse.data.access_token || ''
      }

      if (!authToken) {
        throw new Error('Failed to get authentication token')
      }

      // Step 2: Set auth token and create customer
      apiClient.setAuthToken(authToken)

      const customerResponse = await apiClient.createCustomer({
        email: userData.email,
        password: userData.password,
        first_name: userData.first_name,
        last_name: userData.last_name,
        phone: userData.phone
      })

      if (customerResponse.error || !customerResponse.data?.customer) {
        throw new Error(customerResponse.error || 'Failed to create customer')
      }

      const customer = customerResponse.data.customer

      // Update auth state
      authStore.update(state => ({
        ...state,
        user: customer,
        isAuthenticated: true,
        isLoading: false,
        error: null
      }))

      isAuthenticated.set(true)
      currentUser.set(customer)

      return { success: true, user: customer }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Registration failed'
      authActions.setError(errorMessage)
      authActions.setLoading(false)
      return { success: false, error: errorMessage }
    }
  },

  // Logout
  logout: async () => {
    try {
      // Call Medusa.js logout endpoint if available
      await apiClient.deleteSession()
    } catch (error) {
      console.error('Logout error:', error)
    }

    // Clear auth token
    apiClient.clearAuthToken()

    // Reset auth state
    authStore.set(initialState)
    isAuthenticated.set(false)
    currentUser.set(null)

    // Redirect to home
    goto('/')
  },

  // Check authentication status
  checkAuth: async () => {
    const token = getAuthToken()
    if (!token) {
      return false
    }

    authActions.setLoading(true)

    try {
      // Get customer data from Medusa.js
      const customerResponse = await apiClient.getCustomer()

      if (!customerResponse.data || !customerResponse.data.customer) {
        throw new Error('Authentication check failed')
      }

      const customer = customerResponse.data.customer

      // Update auth state
      authStore.update(state => ({
        ...state,
        user: customer,
        isAuthenticated: true,
        isLoading: false,
        error: null
      }))

      isAuthenticated.set(true)
      currentUser.set(customer)

      return true
    } catch (error) {
      // Clear invalid token
      apiClient.clearAuthToken()

      authStore.set({ ...initialState, isLoading: false })
      isAuthenticated.set(false)
      currentUser.set(null)

      return false
    }
  },

  // Forgot password
  forgotPassword: async (email: string) => {
    authActions.setLoading(true)
    authActions.setError(null)

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to send reset email')
      }

      authActions.setLoading(false)
      return { success: true, message: data.message }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send reset email'
      authActions.setError(errorMessage)
      authActions.setLoading(false)
      return { success: false, error: errorMessage }
    }
  },

  // Reset password
  resetPassword: async (token: string, password: string) => {
    authActions.setLoading(true)
    authActions.setError(null)

    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token, password }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to reset password')
      }

      authActions.setLoading(false)
      return { success: true, message: data.message }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to reset password'
      authActions.setError(errorMessage)
      authActions.setLoading(false)
      return { success: false, error: errorMessage }
    }
  }
}

// Helper function to get auth token
export function getAuthToken(): string | null {
  if (!browser) return null
  return localStorage.getItem('auth_token')
}

// Helper function to get auth headers
export function getAuthHeaders(): Record<string, string> {
  const token = getAuthToken()
  return token ? { 'Authorization': `Bearer ${token}` } : {}
}

// Initialize auth state on app load
if (browser) {
  authActions.checkAuth()
}
