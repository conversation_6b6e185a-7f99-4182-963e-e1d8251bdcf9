import { model } from "@medusajs/framework/utils"

const ReadingProgress = model.define("reading_progress", {
  id: model.id().primaryKey(),
  user_id: model.text(),
  chapter_id: model.text(),
  progress_percentage: model.number().default(0), // 0-100
  last_position: model.text().nullable(), // JSON string with reading position data
  time_spent_minutes: model.number().default(0), // Total time spent reading this chapter
  completed_at: model.dateTime().nullable(), // When user finished reading
  bookmarked: model.boolean().default(false), // User bookmarked this chapter
  notes: model.text().nullable(), // User's personal notes
})

export default ReadingProgress
