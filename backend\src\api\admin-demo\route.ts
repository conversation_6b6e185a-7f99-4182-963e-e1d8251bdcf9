import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const html = `
<!DOCTYPE html>
<html lang="en" dir="ltr" id="html-root">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hebrew Book Store - Admin Dashboard Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            direction: ltr;
            transition: direction 0.3s ease;
        }

        body.rtl {
            direction: rtl;
        }

        .language-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: white;
            border-radius: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 5px;
            display: flex;
            gap: 5px;
        }

        .lang-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            background: transparent;
            color: #666;
        }

        .lang-btn.active {
            background: #4299e1;
            color: white;
        }

        .lang-btn:hover {
            background: #e2e8f0;
        }

        .lang-btn.active:hover {
            background: #3182ce;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
        }
        .card h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #4299e1;
            padding-bottom: 10px;
        }
        .stat-card {
            text-align: center;
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin: 10px 0;
        }
        .test-button {
            background: #4299e1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 3px;
            font-size: 12px;
        }
        .test-button:hover {
            background: #3182ce;
        }
        .test-button.success {
            background: #48bb78;
        }
        .test-button.error {
            background: #e53e3e;
        }
        .api-url {
            background: #2d3748;
            color: #68d391;
            padding: 8px 12px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            margin: 5px 0;
            word-break: break-all;
        }
        .response {
            background: #1a202c;
            color: #e2e8f0;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 10px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 10px;
            display: none;
        }
        .tabs {
            display: flex;
            background: white;
            border-radius: 10px 10px 0 0;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            background: #f8f9fa;
            border-right: 1px solid #e0e0e0;
            transition: all 0.3s;
        }
        .tab:last-child {
            border-right: none;
        }
        .tab.active {
            background: #4299e1;
            color: white;
        }
        .tab-content {
            background: white;
            padding: 20px;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .metric-row:last-child {
            border-bottom: none;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 5px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4299e1, #48bb78);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <!-- Language Switcher -->
    <div class="language-switcher">
        <button class="lang-btn active" onclick="switchLanguage('en')" data-lang="en">🇺🇸 EN</button>
        <button class="lang-btn" onclick="switchLanguage('he')" data-lang="he">🇮🇱 עב</button>
        <button class="lang-btn" onclick="switchLanguage('ru')" data-lang="ru">🇷🇺 РУ</button>
    </div>

    <div class="header">
        <h1 data-en="🛠️ Hebrew Book Store - Admin Dashboard" data-he="🛠️ חנות ספרי עברית - לוח בקרה" data-ru="🛠️ Магазин книг на иврите - Панель администратора">🛠️ Hebrew Book Store - Admin Dashboard</h1>
        <p data-en="Advanced management dashboard for the platform" data-he="לוח בקרה מתקדם לניהול הפלטפורמה" data-ru="Продвинутая панель управления платформой">Advanced management dashboard for the platform</p>
    </div>
    
    <div class="container">
        <!-- Quick Stats -->
        <div class="dashboard-grid">
            <div class="card stat-card">
                <h3>📚 סה"כ פרקים</h3>
                <div class="stat-number" id="total-chapters">-</div>
                <p>פרקים פעילים בפלטפורמה</p>
            </div>
            <div class="card stat-card">
                <h3>💰 הכנסות חודשיות</h3>
                <div class="stat-number" id="monthly-revenue">-</div>
                <p>הכנסות החודש הנוכחי</p>
            </div>
            <div class="card stat-card">
                <h3>👥 משתמשים פעילים</h3>
                <div class="stat-number" id="active-users">-</div>
                <p>משתמשים פעילים השבוע</p>
            </div>
            <div class="card stat-card">
                <h3>📈 שיעור השלמה</h3>
                <div class="stat-number" id="completion-rate">-</div>
                <p>שיעור השלמה ממוצע</p>
            </div>
        </div>

        <!-- Tabbed Interface -->
        <div class="tabs">
            <div class="tab active" onclick="switchTab('chapters')">ניהול פרקים</div>
            <div class="tab" onclick="switchTab('analytics')">אנליטיקה</div>
            <div class="tab" onclick="switchTab('users')">משתמשים</div>
            <div class="tab" onclick="switchTab('api-tests')">בדיקות API</div>
        </div>

        <!-- Chapters Management -->
        <div id="chapters-content" class="tab-content active">
            <div class="card">
                <h3>📖 ניהול פרקים</h3>
                <div class="api-url">GET /admin/chapters</div>
                <button class="test-button" onclick="loadAdminChapters()">טעינת פרקים</button>
                <button class="test-button" onclick="testAPI('/admin/chapters?is_published=false', 'admin-chapters-response')">טיוטות</button>
                <button class="test-button" onclick="testAPI('/admin/chapters?difficulty_level=beginner', 'admin-chapters-response')">מתחילים</button>
                <div id="admin-chapters-response" class="response"></div>
                <div id="chapters-list" class="loading">טוען רשימת פרקים...</div>
            </div>
        </div>

        <!-- Analytics -->
        <div id="analytics-content" class="tab-content">
            <div class="card">
                <h3>📊 אנליטיקה מתקדמת</h3>
                <div class="api-url">GET /admin/analytics</div>
                <button class="test-button" onclick="loadAnalytics()">טעינת אנליטיקה</button>
                <button class="test-button" onclick="testAPI('/admin/analytics?period=7d', 'analytics-response')">7 ימים</button>
                <button class="test-button" onclick="testAPI('/admin/analytics?period=30d', 'analytics-response')">30 ימים</button>
                <button class="test-button" onclick="testAPI('/admin/analytics?metric=revenue', 'analytics-response')">הכנסות</button>
                <div id="analytics-response" class="response"></div>
                <div id="analytics-dashboard" class="loading">טוען נתוני אנליטיקה...</div>
            </div>
        </div>

        <!-- Users -->
        <div id="users-content" class="tab-content">
            <div class="card">
                <h3>👥 ניהול משתמשים</h3>
                <p>בקרוב: ממשק ניהול משתמשים מתקדם</p>
                <div class="metric-row">
                    <span>משתמשים רשומים:</span>
                    <strong>1,247</strong>
                </div>
                <div class="metric-row">
                    <span>מנויים פעילים:</span>
                    <strong>342</strong>
                </div>
                <div class="metric-row">
                    <span>שיעור המרה:</span>
                    <strong>12.3%</strong>
                </div>
            </div>
        </div>

        <!-- API Tests -->
        <div id="api-tests-content" class="tab-content">
            <div class="card">
                <h3>🔧 בדיקות API מקיפות</h3>
                
                <h4>Admin APIs:</h4>
                <div class="api-url">GET /admin/chapters/chapter_1</div>
                <button class="test-button" onclick="testAPI('/admin/chapters/chapter_1', 'api-test-response')">פרק ספציפי</button>
                
                <div class="api-url">GET /admin/analytics?period=30d&metric=revenue</div>
                <button class="test-button" onclick="testAPI('/admin/analytics?period=30d&metric=revenue', 'api-test-response')">אנליטיקת הכנסות</button>
                
                <h4>Public APIs:</h4>
                <div class="api-url">GET /public/search?q=משפחה&type=vocabulary</div>
                <button class="test-button" onclick="testAPI('/public/search?q=משפחה&type=vocabulary', 'api-test-response')">חיפוש מתקדם</button>
                
                <div class="api-url">GET /public/subscriptions/plans</div>
                <button class="test-button" onclick="testAPI('/public/subscriptions/plans', 'api-test-response')">תוכניות מנוי</button>
                
                <div id="api-test-response" class="response"></div>
            </div>
        </div>
    </div>

    <script>
        // Language switching functionality
        let currentLanguage = 'en';

        function switchLanguage(lang) {
            currentLanguage = lang;

            // Update HTML direction
            const htmlRoot = document.getElementById('html-root');
            const body = document.body;

            if (lang === 'he') {
                htmlRoot.setAttribute('dir', 'rtl');
                htmlRoot.setAttribute('lang', 'he');
                body.classList.add('rtl');
            } else {
                htmlRoot.setAttribute('dir', 'ltr');
                htmlRoot.setAttribute('lang', lang);
                body.classList.remove('rtl');
            }

            // Update active language button
            document.querySelectorAll('.lang-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.getAttribute('data-lang') === lang) {
                    btn.classList.add('active');
                }
            });

            // Update all text elements
            document.querySelectorAll('[data-' + lang + ']').forEach(element => {
                const text = element.getAttribute('data-' + lang);
                if (text) {
                    element.textContent = text;
                }
            });

            // Store language preference
            localStorage.setItem('preferred-language', lang);
        }

        // Load saved language preference
        function loadLanguagePreference() {
            const savedLang = localStorage.getItem('preferred-language') || 'en';
            switchLanguage(savedLang);
        }

        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName + '-content').classList.add('active');
            
            // Add active class to selected tab
            event.target.classList.add('active');
        }

        async function testAPI(endpoint, responseId) {
            const responseElement = document.getElementById(responseId);
            const button = event.target;
            
            responseElement.style.display = 'block';
            responseElement.textContent = 'טוען...';
            button.classList.remove('success', 'error');
            
            try {
                const response = await fetch(endpoint);
                const data = await response.json();
                responseElement.textContent = JSON.stringify(data, null, 2);
                button.classList.add('success');
            } catch (error) {
                responseElement.textContent = 'שגיאה: ' + error.message;
                button.classList.add('error');
            }
        }

        async function loadQuickStats() {
            try {
                const response = await fetch('/admin/analytics?period=30d');
                const data = await response.json();
                const analytics = data.analytics;
                
                document.getElementById('total-chapters').textContent = analytics.overview.total_chapters;
                document.getElementById('monthly-revenue').textContent = '$' + analytics.overview.total_revenue.toLocaleString();
                document.getElementById('active-users').textContent = analytics.overview.total_users.toLocaleString();
                document.getElementById('completion-rate').textContent = '76%';
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        async function loadAdminChapters() {
            const container = document.getElementById('chapters-list');
            
            try {
                const response = await fetch('/admin/chapters');
                const data = await response.json();
                
                container.innerHTML = \`
                    <h4>סיכום: \${data.summary.total_chapters} פרקים (\${data.summary.published_chapters} פורסמו, \${data.summary.draft_chapters} טיוטות)</h4>
                    <p><strong>הכנסות כוללות:</strong> $\${data.summary.total_revenue.toLocaleString()}</p>
                    
                    \${data.chapters.map(chapter => \`
                        <div class="metric-row">
                            <div>
                                <strong>\${chapter.title}</strong><br>
                                <small>צפיות: \${chapter.stats.views} | רכישות: \${chapter.stats.purchases} | השלמה: \${chapter.stats.completion_rate}%</small>
                            </div>
                            <div>
                                <span style="padding: 3px 8px; border-radius: 3px; font-size: 11px; background: \${chapter.is_published ? '#48bb78' : '#ed8936'}; color: white;">
                                    \${chapter.is_published ? 'פורסם' : 'טיוטה'}
                                </span>
                            </div>
                        </div>
                    \`).join('')}
                \`;
            } catch (error) {
                container.innerHTML = '<p>שגיאה בטעינת הפרקים: ' + error.message + '</p>';
            }
        }

        async function loadAnalytics() {
            const container = document.getElementById('analytics-dashboard');
            
            try {
                const response = await fetch('/admin/analytics');
                const data = await response.json();
                const analytics = data.analytics;
                
                container.innerHTML = \`
                    <div class="dashboard-grid">
                        <div class="card">
                            <h4>📈 צמיחה</h4>
                            <div class="metric-row">
                                <span>צמיחת הכנסות:</span>
                                <strong style="color: #48bb78;">+\${analytics.overview.growth_metrics.revenue_growth}%</strong>
                            </div>
                            <div class="metric-row">
                                <span>צמיחת משתמשים:</span>
                                <strong style="color: #48bb78;">+\${analytics.overview.growth_metrics.user_growth}%</strong>
                            </div>
                            <div class="metric-row">
                                <span>צמיחת מנויים:</span>
                                <strong style="color: #48bb78;">+\${analytics.overview.growth_metrics.subscription_growth}%</strong>
                            </div>
                        </div>
                        
                        <div class="card">
                            <h4>🏆 פרקים מובילים</h4>
                            \${analytics.revenue.top_earning_chapters.map(chapter => \`
                                <div class="metric-row">
                                    <span>\${chapter.title}</span>
                                    <strong>$\${chapter.revenue.toLocaleString()}</strong>
                                </div>
                            \`).join('')}
                        </div>
                    </div>
                    
                    <div class="card">
                        <h4>🌍 התפלגות גיאוגרפית</h4>
                        \${analytics.users.geographic_distribution.map(geo => \`
                            <div class="metric-row">
                                <span>\${geo.country}</span>
                                <div style="flex: 1; margin: 0 10px;">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: \${geo.percentage}%"></div>
                                    </div>
                                </div>
                                <strong>\${geo.users} (\${geo.percentage}%)</strong>
                            </div>
                        \`).join('')}
                    </div>
                \`;
            } catch (error) {
                container.innerHTML = '<p>שגיאה בטעינת האנליטיקה: ' + error.message + '</p>';
            }
        }

        // Load initial data
        document.addEventListener('DOMContentLoaded', function() {
            // Load language preference first
            loadLanguagePreference();

            // Then load content
            loadQuickStats();
            loadAdminChapters();
        });
    </script>
</body>
</html>
  `;

  res.setHeader('Content-Type', 'text/html; charset=utf-8');
  res.send(html);
}
