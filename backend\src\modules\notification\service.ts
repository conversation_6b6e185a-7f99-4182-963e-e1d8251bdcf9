import { MedusaService } from "@medusajs/framework/utils"
import sgMail from "@sendgrid/mail"

sgMail.setApiKey(process.env.SENDGRID_API_KEY!)

interface EmailTemplate {
  to: string
  subject: string
  text: string
  html: string
}

class NotificationService extends MedusaService({}) {
  private fromEmail = process.env.FROM_EMAIL || "<EMAIL>"
  private frontendUrl = process.env.FRONTEND_URL || "http://localhost:3000"

  async sendEmail(template: EmailTemplate) {
    try {
      const msg = {
        to: template.to,
        from: this.fromEmail,
        subject: template.subject,
        text: template.text,
        html: template.html,
      }
      
      await sgMail.send(msg)
      console.log(`Email sent successfully to ${template.to}`)
      return { success: true }
    } catch (error) {
      console.error("Email sending failed:", error)
      return { success: false, error }
    }
  }

  async sendPurchaseConfirmation(user: any, items: any[]) {
    const itemsList = items.map(item => `<li>${item.name} - $${item.price}</li>`).join("")
    const total = items.reduce((sum, item) => sum + item.price, 0)

    const template: EmailTemplate = {
      to: user.email,
      subject: "תודה על הרכישה - Hebrew Book Store",
      text: `שלום ${user.first_name || ""},\n\nתודה על הרכישה! רכשת את הפריטים הבאים:\n${items.map(i => `- ${i.name}`).join("\n")}\n\nסה"כ: $${total}\n\nכעת תוכל לגשת לחומרים באזור האישי שלך: ${this.frontendUrl}/account`,
      html: `
        <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2c3e50;">תודה על הרכישה!</h1>
          <p>שלום ${user.first_name || ""},</p>
          <p>תודה על הרכישה! רכשת את הפריטים הבאים:</p>
          <ul style="list-style-type: none; padding: 0;">
            ${itemsList}
          </ul>
          <p><strong>סה"כ: $${total}</strong></p>
          <p>כעת תוכל לגשת לחומרים באזור האישי שלך:</p>
          <a href="${this.frontendUrl}/account" style="background-color: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">כניסה לאזור האישי</a>
          <br><br>
          <p>בברכה,<br>צוות Hebrew Book Store</p>
        </div>
      `,
    }

    return await this.sendEmail(template)
  }

  async sendSubscriptionConfirmation(user: any, subscription: any) {
    const expiryDate = new Date(subscription.expires_at).toLocaleDateString('he-IL')
    const subscriptionType = subscription.type === "6_months" ? "6 חודשים" : "12 חודשים"

    const template: EmailTemplate = {
      to: user.email,
      subject: "מנוי חדש הופעל - Hebrew Book Store",
      text: `שלום ${user.first_name || ""},\n\nהמנוי שלך הופעל בהצלחה!\n\nסוג מנוי: ${subscriptionType}\nתאריך תפוגה: ${expiryDate}\n\nכעת תוכל לגשת לכל החומרים באתר: ${this.frontendUrl}/chapters`,
      html: `
        <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #27ae60;">המנוי שלך הופעל!</h1>
          <p>שלום ${user.first_name || ""},</p>
          <p>המנוי שלך הופעל בהצלחה!</p>
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <p><strong>סוג מנוי:</strong> ${subscriptionType}</p>
            <p><strong>תאריך תפוגה:</strong> ${expiryDate}</p>
          </div>
          <p>כעת תוכל לגשת לכל החומרים באתר:</p>
          <a href="${this.frontendUrl}/chapters" style="background-color: #27ae60; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">צפייה בפרקים</a>
          <br><br>
          <p>בברכה,<br>צוות Hebrew Book Store</p>
        </div>
      `,
    }

    return await this.sendEmail(template)
  }

  async sendSubscriptionExpiration(user: any, subscription: any) {
    const expiryDate = new Date(subscription.expires_at).toLocaleDateString('he-IL')
    const daysLeft = Math.ceil((new Date(subscription.expires_at).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))

    const template: EmailTemplate = {
      to: user.email,
      subject: "המנוי שלך עומד להסתיים - Hebrew Book Store",
      text: `שלום ${user.first_name || ""},\n\nהמנוי שלך עומד להסתיים בעוד ${daysLeft} ימים (${expiryDate}).\n\nחדש את המנוי כדי להמשיך ליהנות מהגישה לכל החומרים: ${this.frontendUrl}/account/subscription`,
      html: `
        <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #e74c3c;">המנוי שלך עומד להסתיים</h1>
          <p>שלום ${user.first_name || ""},</p>
          <p>המנוי שלך עומד להסתיים בעוד <strong>${daysLeft} ימים</strong> (${expiryDate}).</p>
          <p>חדש את המנוי כדי להמשיך ליהנות מהגישה לכל החומרים:</p>
          <a href="${this.frontendUrl}/account/subscription" style="background-color: #e74c3c; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">חידוש מנוי</a>
          <br><br>
          <p>בברכה,<br>צוות Hebrew Book Store</p>
        </div>
      `,
    }

    return await this.sendEmail(template)
  }

  async sendWelcomeEmail(user: any) {
    const template: EmailTemplate = {
      to: user.email,
      subject: "ברוכים הבאים ל-Hebrew Book Store",
      text: `שלום ${user.first_name || ""},\n\nברוכים הבאים ל-Hebrew Book Store!\n\nאנחנו שמחים שהצטרפת אלינו. כאן תוכל למצוא ספרים ופרקים באיכות גבוהה בעברית.\n\nהתחל לקרוא עכשיו: ${this.frontendUrl}/chapters`,
      html: `
        <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #3498db;">ברוכים הבאים ל-Hebrew Book Store!</h1>
          <p>שלום ${user.first_name || ""},</p>
          <p>אנחנו שמחים שהצטרפת אלינו!</p>
          <p>ב-Hebrew Book Store תוכל למצוא:</p>
          <ul>
            <li>ספרים ופרקים באיכות גבוהה בעברית</li>
            <li>רמות קושי שונות - מתחילים ועד מתקדמים</li>
            <li>תוכן מעודכן ומעניין</li>
            <li>חוויית קריאה מותאמת אישית</li>
          </ul>
          <a href="${this.frontendUrl}/chapters" style="background-color: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">התחל לקרוא עכשיו</a>
          <br><br>
          <p>בברכה,<br>צוות Hebrew Book Store</p>
        </div>
      `,
    }

    return await this.sendEmail(template)
  }

  async sendPasswordReset(user: any, resetToken: string) {
    const resetUrl = `${this.frontendUrl}/reset-password?token=${resetToken}`

    const template: EmailTemplate = {
      to: user.email,
      subject: "איפוס סיסמה - Hebrew Book Store",
      text: `שלום ${user.first_name || ""},\n\nקיבלנו בקשה לאיפוס הסיסמה שלך.\n\nלחץ על הקישור הבא כדי לאפס את הסיסמה: ${resetUrl}\n\nהקישור תקף למשך 24 שעות.\n\nאם לא ביקשת איפוס סיסמה, התעלם מהודעה זו.`,
      html: `
        <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #e74c3c;">איפוס סיסמה</h1>
          <p>שלום ${user.first_name || ""},</p>
          <p>קיבלנו בקשה לאיפוס הסיסמה שלך.</p>
          <p>לחץ על הכפתור הבא כדי לאפס את הסיסמה:</p>
          <a href="${resetUrl}" style="background-color: #e74c3c; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">איפוס סיסמה</a>
          <br><br>
          <p><small>הקישור תקף למשך 24 שעות.</small></p>
          <p><small>אם לא ביקשת איפוס סיסמה, התעלם מהודעה זו.</small></p>
          <br>
          <p>בברכה,<br>צוות Hebrew Book Store</p>
        </div>
      `,
    }

    return await this.sendEmail(template)
  }
}

export default NotificationService
