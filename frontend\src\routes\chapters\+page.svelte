<script lang="ts">
	import { onMount } from 'svelte'
	import { _ } from '$lib/i18n'
	import { getCurrentLocale, isRTL } from '$lib/i18n'
	import { topics, productTypes, difficultyLevels } from '$lib/data/products'
	import { productsStore, productActions, isLoadingProducts, productsError } from '$lib/stores/products'
	import { cartActions, cartStore, cartItemCount } from '$lib/stores/cart'
	import { loadCartFromStorage } from '$lib/stores/cart'
	import type { Product, ProductFilter, ProductSort } from '$lib/types/products'

	let currentLocale = 'en'
	let rtl = false
	let filteredProducts: Product[] = []

	// Filters
	let sort: ProductSort = { field: 'title', direction: 'asc' }
	let searchQuery = ''
	let selectedTopics: string[] = []
	let selectedDifficulties: string[] = []
	let selectedTypes: string[] = []
	let priceRange = { min: 0, max: 100 }
	let showFeaturedOnly = false

	// UI state
	let viewMode: 'grid' | 'list' = 'grid'

	$: currentLocale = getCurrentLocale()
	$: rtl = isRTL(currentLocale)
	$: cart = $cartStore
	$: itemCount = $cartItemCount
	$: allProducts = $productsStore
	$: isLoading = $isLoadingProducts
	$: error = $productsError

	onMount(async () => {
		loadCartFromStorage()
		await productActions.loadProducts()
		applyFilters()
	})

	function applyFilters() {
		isLoading = true

		// Start with published products from store
		// Note: Store API only returns published products, so all products should have is_published: true
		let result = $productsStore.filter(product => product.is_published)

		// Apply search
		if (searchQuery.trim()) {
			const query = searchQuery.toLowerCase()
			result = result.filter(product =>
				product.title.toLowerCase().includes(query) ||
				product.description.toLowerCase().includes(query) ||
				product.tags.some(tag => tag.toLowerCase().includes(query))
			)
			console.log('🔍 After search filter:', result.length)
		}

		// Apply topic filter
		if (selectedTopics.length > 0) {
			result = result.filter(product =>
				product.topics.some(topic => selectedTopics.includes(topic))
			)
		}

		// Apply difficulty filter
		if (selectedDifficulties.length > 0) {
			result = result.filter(product =>
				selectedDifficulties.includes(product.difficulty_level)
			)
		}

		// Apply type filter
		if (selectedTypes.length > 0) {
			result = result.filter(product =>
				selectedTypes.includes(product.type)
			)
		}

		// Apply price filter
		result = result.filter(product =>
			product.price >= priceRange.min && product.price <= priceRange.max
		)

		// Apply featured filter
		if (showFeaturedOnly) {
			result = result.filter(product => product.is_featured)
		}

		// Apply sorting
		result.sort((a, b) => {
			let aValue: any = a[sort.field]
			let bValue: any = b[sort.field]

			if (sort.field === 'created_at') {
				aValue = new Date(aValue).getTime()
				bValue = new Date(bValue).getTime()
			} else if (sort.field === 'rating') {
				aValue = a.stats.rating
				bValue = b.stats.rating
			} else if (sort.field === 'popularity') {
				aValue = a.stats.purchases
				bValue = b.stats.purchases
			}

			if (sort.direction === 'asc') {
				return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
			} else {
				return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
			}
		})

		filteredProducts = result
		isLoading = false
	}

	function addToCart(product: Product) {
		cartActions.addItem(product)
	}

	function formatPrice(price: number): string {
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency: 'USD'
		}).format(price)
	}

	function getDiscountPercentage(originalPrice: number, currentPrice: number): number {
		return Math.round(((originalPrice - currentPrice) / originalPrice) * 100)
	}

	function clearFilters() {
		searchQuery = ''
		selectedTopics = []
		selectedDifficulties = []
		selectedTypes = []
		priceRange = { min: 0, max: 100 }
		showFeaturedOnly = false
		applyFilters()
	}

	// Reactive statements for filter changes
	$: searchQuery, selectedTopics, selectedDifficulties, selectedTypes, priceRange, showFeaturedOnly, sort, $productsStore, applyFilters()
</script>

<svelte:head>
	<title>Hebrew Learning Catalog - Hebrew Book Store</title>
	<meta name="description" content="Browse our complete catalog of Hebrew learning products - chapters, subscriptions, and bundles" />
</svelte:head>

<div class="min-h-screen" style="background: var(--color-bg-primary);" class:rtl>
	<!-- Page Header -->
	<div class="py-12" style="background: var(--color-bg-accent);">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center">
				<h1 class="font-book-title text-4xl md:text-5xl font-bold mb-4" style="color: var(--color-text-primary);" class:text-right={rtl}>
					{$_('catalog.title')}
				</h1>
				<p class="font-book-text text-lg max-w-3xl mx-auto leading-relaxed" style="color: var(--color-text-secondary);" class:text-right={rtl}>
					{$_('catalog.subtitle')}
				</p>
			</div>
		</div>
	</div>

	<!-- Main Content -->
	<main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
		<!-- Filters and Search -->
		<div class="mb-8">
			<div class="flex flex-col lg:flex-row gap-6">
				<!-- Search Bar -->
				<div class="flex-1">
					<div class="relative">
						<input
							type="text"
							placeholder="{$_('catalog.search_placeholder')}"
							bind:value={searchQuery}
							class="w-full px-4 py-3 pl-10 font-book-text border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
							style="background: var(--color-bg-primary);"
						/>
						<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
							<svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
							</svg>
						</div>
					</div>
				</div>

				<!-- View Mode Toggle -->
				<div class="flex items-center gap-2">
					<button
						class="p-2 rounded-lg transition-colors {viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}"
						on:click={() => viewMode = 'grid'}
					>
						<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
							<path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
						</svg>
					</button>
					<button
						class="p-2 rounded-lg transition-colors {viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}"
						on:click={() => viewMode = 'list'}
					>
						<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
						</svg>
					</button>
				</div>

				<!-- Cart Icon -->
				<div class="relative">
					<button class="p-3 rounded-lg transition-colors hover:bg-gray-100" style="background: var(--color-bg-primary);">
						<svg class="w-6 h-6" style="color: var(--color-text-primary);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"/>
						</svg>
						{#if itemCount > 0}
							<span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
								{itemCount}
							</span>
						{/if}
					</button>
				</div>
			</div>
		</div>

		<!-- Quick Filters -->
		<div class="mb-8">
			<div class="flex flex-wrap gap-4">
				<!-- Featured Toggle -->
				<label class="flex items-center">
					<input
						type="checkbox"
						bind:checked={showFeaturedOnly}
						class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
					/>
					<span class="ml-2 font-book-text text-sm" style="color: var(--color-text-secondary);">{$_('catalog.featured_only')}</span>
				</label>

				<!-- Product Type Filter -->
				<select
					bind:value={selectedTypes}
					multiple
					class="px-3 py-2 border border-gray-300 rounded-lg font-book-text text-sm focus:ring-2 focus:ring-blue-500"
					style="background: var(--color-bg-primary);"
				>
					<option value="">{$_('catalog.all_types')}</option>
					<option value="chapter">{$_('catalog.product_types.chapter')}</option>
					<option value="subscription">{$_('catalog.product_types.subscription')}</option>
					<option value="bundle">{$_('catalog.product_types.bundle')}</option>
				</select>

				<!-- Difficulty Filter -->
				<select
					bind:value={selectedDifficulties}
					multiple
					class="px-3 py-2 border border-gray-300 rounded-lg font-book-text text-sm focus:ring-2 focus:ring-blue-500"
					style="background: var(--color-bg-primary);"
				>
					<option value="">{$_('catalog.all_levels')}</option>
					<option value="beginner">{$_('home.difficulty.beginner')}</option>
					<option value="intermediate">{$_('home.difficulty.intermediate')}</option>
					<option value="advanced">{$_('home.difficulty.advanced')}</option>
				</select>

				<!-- Sort -->
				<select
					bind:value={sort.field}
					class="px-3 py-2 border border-gray-300 rounded-lg font-book-text text-sm focus:ring-2 focus:ring-blue-500"
					style="background: var(--color-bg-primary);"
				>
					<option value="title">{$_('catalog.sort_by_title')}</option>
					<option value="price">{$_('catalog.sort_by_price')}</option>
					<option value="rating">{$_('catalog.sort_by_rating')}</option>
					<option value="popularity">{$_('catalog.sort_by_popularity')}</option>
					<option value="created_at">{$_('catalog.sort_by_date')}</option>
				</select>

				<!-- Clear Filters -->
				<button
					on:click={clearFilters}
					class="px-4 py-2 text-sm font-book-text text-gray-600 hover:text-gray-800 underline"
				>
					{$_('catalog.clear_filters')}
				</button>
			</div>
		</div>

		<!-- Results Count -->
		<div class="mb-6">
			<p class="font-book-text text-sm" style="color: var(--color-text-secondary);">
				{$_('catalog.showing_products').replace('{count}', filteredProducts.length.toString())}
			</p>
		</div>

		<!-- Products Grid/List -->
		{#if isLoading}
			<div class="flex justify-center items-center py-12">
				<div class="text-center">
					<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
					<p class="font-book-text" style="color: var(--color-text-secondary);">{$_('common.loading')}</p>
				</div>
			</div>
		{:else if filteredProducts.length === 0}
			<div class="text-center py-12">
				<svg class="mx-auto h-12 w-12" style="color: var(--color-text-light);" fill="none" viewBox="0 0 24 24" stroke="currentColor">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5"/>
				</svg>
				<h3 class="mt-2 font-book-title text-lg font-medium" style="color: var(--color-text-primary);">{$_('catalog.no_products_found')}</h3>
				<p class="mt-1 font-book-text text-sm" style="color: var(--color-text-secondary);">{$_('catalog.no_products_description')}</p>
				<button
					on:click={clearFilters}
					class="mt-4 btn-classic px-6 py-2"
				>
					{$_('catalog.clear_all_filters')}
				</button>
			</div>
		{:else}
			<div class="{viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-6'}">
				{#each filteredProducts as product (product.id)}
					<div class="card-classic overflow-hidden">
						<!-- Product Image/Icon -->
						<div class="h-48 flex items-center justify-center" style="background: var(--color-bg-subtle);">
							<div class="text-center">
								{#if product.type === 'chapter'}
									<div class="text-6xl mb-2">📖</div>
								{:else if product.type === 'subscription'}
									<div class="text-6xl mb-2">⭐</div>
								{:else if product.type === 'bundle'}
									<div class="text-6xl mb-2">📚</div>
								{/if}
								<div class="font-book-text text-xs uppercase tracking-wide" style="color: var(--color-text-accent);">
									{$_(`catalog.product_types.${product.type}`)}
								</div>
							</div>
						</div>

						<!-- Product Info -->
						<div class="p-6">
							<!-- Header -->
							<div class="flex items-start justify-between mb-4">
								<div class="flex-1">
									<h3 class="font-book-title text-lg font-bold mb-2" style="color: var(--color-text-primary);">
										{product.title}
									</h3>
									<p class="font-book-text text-sm leading-relaxed" style="color: var(--color-text-secondary);">
										{product.short_description}
									</p>
								</div>
								{#if product.is_featured}
									<span class="ml-2 px-2 py-1 text-xs font-medium rounded-full" style="background: var(--color-primary); color: white;">
										{$_('catalog.featured')}
									</span>
								{/if}
							</div>

							<!-- Tags -->
							<div class="flex flex-wrap gap-2 mb-4">
								<span class="px-2 py-1 text-xs font-medium rounded" style="background: var(--color-bg-accent); color: var(--color-text-accent);">
									{$_(`home.difficulty.${product.difficulty_level}`)}
								</span>
								{#if product.reading_time_minutes > 0}
									<span class="px-2 py-1 text-xs font-medium rounded" style="background: var(--color-bg-accent); color: var(--color-text-accent);">
										{product.reading_time_minutes} min
									</span>
								{/if}
								{#if product.stats.rating > 0}
									<span class="px-2 py-1 text-xs font-medium rounded flex items-center" style="background: var(--color-bg-accent); color: var(--color-text-accent);">
										⭐ {product.stats.rating.toFixed(1)}
									</span>
								{/if}
							</div>

							<!-- Price -->
							<div class="flex items-center justify-between mb-4">
								<div>
									{#if product.original_price && product.original_price > product.price}
										<div class="flex items-center gap-2">
											<span class="font-book-title text-2xl font-bold" style="color: var(--color-primary);">
												{formatPrice(product.price)}
											</span>
											<span class="text-sm line-through" style="color: var(--color-text-light);">
												{formatPrice(product.original_price)}
											</span>
											<span class="px-2 py-1 text-xs font-bold rounded" style="background: var(--color-primary); color: white;">
												-{getDiscountPercentage(product.original_price, product.price)}%
											</span>
										</div>
									{:else}
										<span class="font-book-title text-2xl font-bold" style="color: var(--color-primary);">
											{formatPrice(product.price)}
										</span>
									{/if}
									{#if product.duration}
										<div class="text-xs" style="color: var(--color-text-light);">
											{product.duration === '6m' ? $_('catalog.months_6') : $_('catalog.months_12')}
										</div>
									{/if}
								</div>
							</div>

							<!-- Actions -->
							<div class="flex gap-2">
								<button
									on:click={() => addToCart(product)}
									class="btn-classic flex-1 text-center text-sm py-2"
								>
									{$_('catalog.add_to_cart')}
								</button>
								<button class="decorative-border px-4 py-2 font-book-text text-sm font-medium transition-all duration-300 hover:shadow-md" style="background: var(--color-bg-subtle); color: var(--color-text-accent); border-color: var(--color-border);">
									{$_('catalog.preview')}
								</button>
							</div>
						</div>
					</div>
				{/each}
			</div>
		{/if}
	</main>
</div>
