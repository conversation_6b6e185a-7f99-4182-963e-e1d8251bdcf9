import { writable, derived } from 'svelte/store'
import type { Chapter } from '$lib/api/client'
import apiClient from '$lib/api/client'

export interface ChaptersState {
  chapters: Chapter[]
  currentChapter: Chapter | null
  isLoading: boolean
  error: string | null
}

const initialState: ChaptersState = {
  chapters: [],
  currentChapter: null,
  isLoading: false,
  error: null,
}

function createChaptersStore() {
  const { subscribe, set, update } = writable<ChaptersState>(initialState)

  return {
    subscribe,

    // Load all chapters
    loadChapters: async () => {
      update(state => ({ ...state, isLoading: true, error: null }))

      try {
        const response = await apiClient.getChapters()
        
        if (response.error) {
          throw new Error(response.error)
        }

        update(state => ({
          ...state,
          chapters: response.data?.chapters || [],
          isLoading: false,
        }))

        return { success: true }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to load chapters'
        update(state => ({
          ...state,
          isLoading: false,
          error: errorMessage,
        }))
        return { success: false, error: errorMessage }
      }
    },

    // Load specific chapter
    loadChapter: async (chapterId: string) => {
      update(state => ({ ...state, isLoading: true, error: null }))

      try {
        const response = await apiClient.getChapter(chapterId)
        
        if (response.error) {
          throw new Error(response.error)
        }

        update(state => ({
          ...state,
          currentChapter: response.data?.chapter || null,
          isLoading: false,
        }))

        return { success: true, data: response.data }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to load chapter'
        update(state => ({
          ...state,
          isLoading: false,
          error: errorMessage,
        }))
        return { success: false, error: errorMessage }
      }
    },

    // Clear current chapter
    clearCurrentChapter: () => {
      update(state => ({
        ...state,
        currentChapter: null,
      }))
    },

    // Clear error
    clearError: () => {
      update(state => ({ ...state, error: null }))
    },

    // Reset store
    reset: () => {
      set(initialState)
    },
  }
}

export const chaptersStore = createChaptersStore()

// Derived stores for computed values
export const freeChapters = derived(
  chaptersStore,
  $chaptersStore => $chaptersStore.chapters.filter(chapter => chapter.is_free)
)

export const premiumChapters = derived(
  chaptersStore,
  $chaptersStore => $chaptersStore.chapters.filter(chapter => !chapter.is_free)
)
