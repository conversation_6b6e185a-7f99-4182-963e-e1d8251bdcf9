import { model } from "@medusajs/framework/utils"

const Translation = model.define("translation", {
  id: model.id().primary<PERSON>ey(),
  entity_id: model.text(), // ID of the entity being translated
  entity_type: model.text(), // "chapter", "product", "category", etc.
  language: model.text(), // Language code: "he", "ru", "en"
  field_name: model.text(), // Field being translated: "title", "description", "content"
  translated_value: model.text(), // The translated content
  is_approved: model.boolean().default(false), // For translation review workflow
  translator_id: model.text().nullable(), // ID of translator if applicable
})

export default Translation
