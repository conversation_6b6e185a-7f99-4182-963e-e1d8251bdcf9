-- Hebrew Book Store - Test Data Initialization
-- This script creates necessary users and basic data for testing
-- Note: API keys should be created through Medusa.js API for proper salt/hash generation

-- Create admin user
INSERT INTO "user" (id, email, first_name, last_name, created_at, updated_at) 
VALUES (
  'admin_hebrew_book', 
  '<EMAIL>', 
  'Admin', 
  'User',
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Create test sales channel
INSERT INTO sales_channel (id, name, description, is_disabled, created_at, updated_at)
VALUES (
  'sc_hebrew_book_default',
  'Hebrew Book Store',
  'Default sales channel for Hebrew Book Store',
  false,
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Link publishable key to sales channel
INSERT INTO publishable_api_key_sales_channel (publishable_key_id, sales_channel_id)
VALUES (
  'pk_test_hebrew_book_dev',
  'sc_hebrew_book_default'
) ON CONFLICT DO NOTHING;

-- Create default region
INSERT INTO region (id, name, currency_code, created_at, updated_at)
VALUES (
  'reg_hebrew_book_default',
  'Default Region',
  'usd',
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Create default store
INSERT INTO store (id, name, default_sales_channel_id, default_region_id, created_at, updated_at)
VALUES (
  'store_hebrew_book',
  'Hebrew Book Store',
  'sc_hebrew_book_default',
  'reg_hebrew_book_default',
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Create product types
INSERT INTO product_type (id, value, created_at, updated_at)
VALUES 
  ('pt_chapter', 'chapter', NOW(), NOW()),
  ('pt_subscription', 'subscription', NOW(), NOW()),
  ('pt_bundle', 'bundle', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Create product collections
INSERT INTO product_collection (id, title, handle, created_at, updated_at)
VALUES 
  ('pc_hebrew_basics', 'Hebrew Basics', 'hebrew-basics', NOW(), NOW()),
  ('pc_intermediate', 'Intermediate Hebrew', 'intermediate-hebrew', NOW(), NOW()),
  ('pc_advanced', 'Advanced Hebrew', 'advanced-hebrew', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Display created data
SELECT 'API Keys Created:' as info;
SELECT id, token, type, title FROM api_key WHERE id LIKE '%hebrew_book%';

SELECT 'Users Created:' as info;
SELECT id, email, first_name, last_name FROM "user" WHERE id LIKE '%hebrew_book%';

SELECT 'Sales Channels Created:' as info;
SELECT id, name, description FROM sales_channel WHERE id LIKE '%hebrew_book%';

SELECT 'Setup completed successfully!' as result;
