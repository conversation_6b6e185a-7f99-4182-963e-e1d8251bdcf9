# 🚀 Быстрая настройка Admin Auth - Medusa.js v2

## ⚡ TL;DR - Быстрый старт

```bash
# 1. Создать invite
cd backend
medusa user --email <EMAIL> --invite

# 2. Скопировать токен из вывода
# 3. Открыть: http://localhost:5173/admin/invite?token=[TOKEN]
# 4. Установить пароль: admin123
# 5. Войти: http://localhost:5173/admin/login
```

## 📋 Чек-лист

- [ ] Medusa.js сервер запущен (`yarn dev` в папке backend)
- [ ] PostgreSQL контейнер запущен
- [ ] Medusa CLI установлен (`npm install -g @medusajs/cli ts-node`)
- [ ] Создан invite через CLI
- [ ] Активирован invite в браузере
- [ ] Установлен пароль
- [ ] Успешный вход в админку

## ❌ Что НЕ работает в v2

```bash
# ❌ Не работает - создает пользователя без auth identity
medusa user --email <EMAIL> --password admin123

# ❌ Не работает - нет поля password_hash в таблице user
INSERT INTO "user" (email, password_hash) VALUES (...);

# ❌ Не работает - сложная структура auth_identity + provider_identity
UPDATE "user" SET password = '...' WHERE email = '...';
```

## ✅ Что работает в v2

```bash
# ✅ Правильно - создает invite с токеном
medusa user --email <EMAIL> --invite

# ✅ Правильно - активация через веб-интерфейс
http://localhost:5173/admin/invite?token=[TOKEN]

# ✅ Правильно - вход после активации
http://localhost:5173/admin/login
```

## 🔧 Troubleshooting

### Проблема: "User already exists"
```bash
# Решение: удалить старого пользователя
docker exec -it hebrew-book-store-postgres psql -U medusa_user -d hebrew_book_clean -c "DELETE FROM \"user\" WHERE email = '<EMAIL>';"
```

### Проблема: "Cannot find module 'tsconfig-paths'"
```bash
# Решение: установить зависимости
npm install -g ts-node tsconfig-paths
```

### Проблема: "Invite token expired"
```bash
# Решение: создать новый invite
medusa user --email <EMAIL> --invite
```

## 📞 Если ничего не помогает

1. **Проверьте логи Medusa.js** в терминале где запущен `yarn dev`
2. **Проверьте консоль браузера** (F12) на странице invite
3. **Убедитесь, что все сервисы запущены**:
   ```bash
   docker ps | grep postgres  # PostgreSQL
   curl http://localhost:9000/health  # Medusa.js
   curl http://localhost:5173  # Frontend
   ```

## 🎯 Результат

После успешной настройки:
- ✅ Admin Auth работает полностью
- ✅ Можно войти в админку с email/password
- ✅ Доступны все админские функции
- ✅ API интеграция работает

**Время настройки**: ~2 минуты  
**Сложность**: Легко (если знать правильную процедуру)
