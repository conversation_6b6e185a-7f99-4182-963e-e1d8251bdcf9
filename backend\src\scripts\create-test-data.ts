import { MedusaContainer } from "@medusajs/framework/types"

export default async function createTestData(container: MedusaContainer) {
  console.log("🚀 Creating test data for Hebrew Book Store...")

  try {
    // Get the API key service
    const apiKeyService = container.resolve("apiKeyService")
    
    // Create publishable API key
    console.log("Creating publishable API key...")
    const publishableKey = await apiKeyService.create({
      title: "Hebrew Book Store Development Key",
      type: "publishable",
      created_by: "system"
    })
    console.log(`✅ Publishable key created: ${publishableKey.token}`)

    // Create secret API key  
    console.log("Creating secret API key...")
    const secretKey = await apiKeyService.create({
      title: "Hebrew Book Store Development Secret",
      type: "secret", 
      created_by: "system"
    })
    console.log(`✅ Secret key created: ${secretKey.token}`)

    // Get user service
    const userService = container.resolve("userService")
    
    // Create admin user
    console.log("Creating admin user...")
    const adminUser = await userService.create({
      email: "<EMAIL>",
      first_name: "Admin",
      last_name: "User"
    })
    console.log(`✅ Admin user created: ${adminUser.email}`)

    // Get sales channel service
    const salesChannelService = container.resolve("salesChannelService")
    
    // Create default sales channel
    console.log("Creating sales channel...")
    const salesChannel = await salesChannelService.create({
      name: "Hebrew Book Store",
      description: "Default sales channel for Hebrew Book Store"
    })
    console.log(`✅ Sales channel created: ${salesChannel.name}`)

    // Link publishable key to sales channel
    console.log("Linking API key to sales channel...")
    await apiKeyService.addSalesChannels(publishableKey.id, [salesChannel.id])
    console.log("✅ API key linked to sales channel")

    console.log("\n🎉 Test data creation completed successfully!")
    console.log("\n📋 Summary:")
    console.log(`   • Publishable Key: ${publishableKey.token}`)
    console.log(`   • Secret Key: ${secretKey.token}`)
    console.log(`   • Admin User: ${adminUser.email}`)
    console.log(`   • Sales Channel: ${salesChannel.name}`)
    
    return {
      publishableKey: publishableKey.token,
      secretKey: secretKey.token,
      adminUser: adminUser.email,
      salesChannel: salesChannel.name
    }

  } catch (error) {
    console.error("❌ Error creating test data:", error)
    throw error
  }
}
