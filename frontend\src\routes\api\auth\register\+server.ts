import { json } from '@sveltejs/kit'
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types'
import { MEDUSA_BACKEND_URL } from '$env/static/private'

export const POST: RequestHandler = async ({ request, fetch }) => {
  try {
    const { email, password, first_name, last_name, phone } = await request.json()

    // Validate input
    if (!email || !password || !first_name || !last_name) {
      return json(
        { message: 'Email, password, first name, and last name are required' },
        { status: 400 }
      )
    }

    // Call Medusa.js customer creation endpoint
    const medusaResponse = await fetch(`${MEDUSA_BACKEND_URL}/store/customers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        password,
        first_name,
        last_name,
        phone: phone || undefined,
      }),
    })

    const medusaData = await medusaResponse.json()

    if (!medusaResponse.ok) {
      return json(
        { message: medusaData.message || 'Registration failed' },
        { status: 400 }
      )
    }

    // Now login the user to get access token
    const loginResponse = await fetch(`${MEDUSA_BACKEND_URL}/store/auth`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    })

    const loginData = await loginResponse.json()

    if (!loginResponse.ok) {
      return json(
        { message: 'Registration successful but login failed' },
        { status: 500 }
      )
    }

    return json({
      success: true,
      token: loginData.access_token,
      customer: medusaData.customer,
      message: 'Registration successful'
    })

  } catch (error) {
    console.error('Registration error:', error)
    return json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
