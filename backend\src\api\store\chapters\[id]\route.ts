import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { BOOK_MODULE } from "../../../../modules/book"

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const bookService = req.scope.resolve(BOOK_MODULE)
  const { id } = req.params
  
  try {
    const chapter = await bookService.getChapter(id)
    
    if (!chapter) {
      return res.status(404).json({ message: "Chapter not found" })
    }

    if (!chapter.is_published) {
      return res.status(404).json({ message: "Chapter not found" })
    }

    // Check if user is authenticated
    const userId = req.user?.id

    let hasAccess = false
    let accessType = null

    if (chapter.is_free) {
      hasAccess = true
      accessType = "free"
    } else if (userId) {
      hasAccess = await bookService.checkUserAccess(userId, id)
      if (hasAccess) {
        // Determine access type
        const directAccess = await bookService.list("UserChapterAccess", {
          user_id: userId,
          chapter_id: id
        })
        if (directAccess.length > 0) {
          accessType = directAccess[0].access_type
        }
      }
    }

    // Prepare response based on access level
    const response: any = {
      id: chapter.id,
      title: chapter.title,
      difficulty_level: chapter.difficulty_level,
      order_in_book: chapter.order_in_book,
      language: chapter.language,
      price: chapter.price,
      is_free: chapter.is_free,
      reading_time_minutes: chapter.reading_time_minutes,
      tags: chapter.tags ? JSON.parse(chapter.tags) : [],
      audio_url: chapter.audio_url,
      video_url: chapter.video_url,
      created_at: chapter.created_at,
      has_access: hasAccess,
      access_type: accessType
    }

    if (hasAccess) {
      // User has access - return full content
      response.content = chapter.content
      
      // Update reading progress if user is authenticated
      if (userId) {
        // This could be done asynchronously
        bookService.updateReadingProgress(userId, id, {
          last_position: "start",
          updated_at: new Date()
        }).catch(console.error)
      }
    } else {
      // User doesn't have access - return only preview
      response.preview_content = chapter.preview_content
    }

    res.json({ chapter: response })
  } catch (error) {
    console.error("Error fetching chapter:", error)
    res.status(500).json({ 
      message: "Internal server error",
      error: process.env.NODE_ENV === "development" ? error.message : undefined
    })
  }
}
