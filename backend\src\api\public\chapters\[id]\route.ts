import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { BOOK_MODULE } from "../../../../modules/book"

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:5173')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
  res.setHeader('Access-Control-Allow-Credentials', 'true')

  const { id } = req.params

  try {
    const bookService = req.scope.resolve(BOOK_MODULE)
    
    // Mock chapters data
    const mockChapters = {
      "chapter_1": {
        id: "chapter_1",
        title: "פרק ראשון - מבוא לעברית",
        content: `
          <div dir="rtl" style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h1>מבוא לעברית</h1>
            <p>ברוכים הבאים לפרק הראשון בלימוד עברית!</p>
            <p>בפרק זה נלמד על:</p>
            <ul>
              <li>האלפבית העברי</li>
              <li>צלילים בסיסיים</li>
              <li>מילים ראשונות</li>
            </ul>
            <h2>האלפבית העברי</h2>
            <p>האלפבית העברי מכיל 22 אותיות:</p>
            <p style="font-size: 1.5em; text-align: center;">א ב ג ד ה ו ז ח ט י כ ל מ נ ס ע פ צ ק ר ש ת</p>
            <h2>תרגילים</h2>
            <p>נסו לקרוא את המילים הבאות:</p>
            <ul>
              <li>שלום</li>
              <li>תודה</li>
              <li>בוקר טוב</li>
            </ul>
          </div>
        `,
        preview_content: "ברוכים הבאים לפרק הראשון בלימוד עברית! בפרק זה נלמד על האלפבית העברי, צלילים בסיסיים ומילים ראשונות...",
        difficulty_level: "beginner",
        order_in_book: 1,
        language: "he",
        price: 9.99,
        is_free: true,
        reading_time_minutes: 15,
        tags: ["אלפבית", "בסיסי", "מתחילים"],
        audio_url: null,
        video_url: null,
        created_at: new Date().toISOString()
      },
      "chapter_2": {
        id: "chapter_2",
        title: "פרק שני - מילים בסיסיות",
        content: `
          <div dir="rtl" style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h1>מילים בסיסיות בעברית</h1>
            <p>בפרק זה נרחיב את אוצר המילים שלנו.</p>
            <h2>משפחה</h2>
            <ul>
              <li>אבא - Father</li>
              <li>אמא - Mother</li>
              <li>אח - Brother</li>
              <li>אחות - Sister</li>
            </ul>
            <h2>צבעים</h2>
            <ul>
              <li>אדום - Red</li>
              <li>כחול - Blue</li>
              <li>ירוק - Green</li>
              <li>צהוב - Yellow</li>
            </ul>
            <h2>מספרים</h2>
            <ul>
              <li>אחד - One</li>
              <li>שניים - Two</li>
              <li>שלושה - Three</li>
              <li>ארבעה - Four</li>
            </ul>
          </div>
        `,
        preview_content: "בפרק זה נרחיב את אוצר המילים שלנו ונלמד מילים בסיסיות על משפחה, צבעים ומספרים...",
        difficulty_level: "beginner",
        order_in_book: 2,
        language: "he",
        price: 12.99,
        is_free: false,
        reading_time_minutes: 20,
        tags: ["מילים", "משפחה", "צבעים", "מספרים"],
        audio_url: null,
        video_url: null,
        created_at: new Date().toISOString()
      },
      "chapter_3": {
        id: "chapter_3",
        title: "פרק שלישי - משפטים ראשונים",
        content: `
          <div dir="rtl" style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h1>בניית משפטים ראשונים</h1>
            <p>עכשיו שאנחנו יודעים מילים בסיסיות, בואו נלמד לבנות משפטים.</p>
            <h2>מבנה המשפט</h2>
            <p>במשפט עברי בסיסי יש לנו:</p>
            <ul>
              <li>נושא (מי עושה את הפעולה)</li>
              <li>פועל (מה נעשה)</li>
              <li>מושא (על מי או מה)</li>
            </ul>
            <h2>דוגמאות</h2>
            <ul>
              <li>אני אוהב ספרים</li>
              <li>היא קוראת עיתון</li>
              <li>הם הולכים הביתה</li>
            </ul>
            <h2>תרגילים</h2>
            <p>נסו לבנות משפטים עם המילים שלמדנו:</p>
            <ul>
              <li>אבא + קורא + ספר</li>
              <li>אמא + אוהבת + פרחים</li>
            </ul>
          </div>
        `,
        preview_content: "עכשיו שאנחנו יודעים מילים בסיסיות, בואו נלמד לבנות משפטים ראשונים...",
        difficulty_level: "intermediate",
        order_in_book: 3,
        language: "he",
        price: 15.99,
        is_free: false,
        reading_time_minutes: 25,
        tags: ["משפטים", "דקדוק", "תרגילים"],
        audio_url: null,
        video_url: null,
        created_at: new Date().toISOString()
      }
    }

    const chapter = mockChapters[id]
    
    if (!chapter) {
      return res.status(404).json({ message: "Chapter not found" })
    }

    // Check if user is authenticated (for now, assume no authentication)
    const userId = req.user?.id
    let hasAccess = false
    let accessType = null

    if (chapter.is_free) {
      hasAccess = true
      accessType = "free"
    }

    // Prepare response based on access level
    const response: any = {
      id: chapter.id,
      title: chapter.title,
      difficulty_level: chapter.difficulty_level,
      order_in_book: chapter.order_in_book,
      language: chapter.language,
      price: chapter.price,
      is_free: chapter.is_free,
      reading_time_minutes: chapter.reading_time_minutes,
      tags: chapter.tags,
      audio_url: chapter.audio_url,
      video_url: chapter.video_url,
      created_at: chapter.created_at,
      has_access: hasAccess,
      access_type: accessType
    }

    if (hasAccess) {
      // User has access - return full content
      response.content = chapter.content
    } else {
      // User doesn't have access - return only preview
      response.preview_content = chapter.preview_content
      response.message = "Purchase required to access full content"
    }

    res.json({ chapter: response })
  } catch (error) {
    console.error("Error fetching chapter:", error)
    res.status(500).json({ 
      message: "Internal server error",
      error: error.message
    })
  }
}

export async function OPTIONS(req: MedusaRequest, res: MedusaResponse) {
  // Set CORS headers for preflight request
  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:5173')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
  res.setHeader('Access-Control-Allow-Credentials', 'true')
  res.setHeader('Access-Control-Max-Age', '86400') // 24 hours

  res.status(200).end()
}
