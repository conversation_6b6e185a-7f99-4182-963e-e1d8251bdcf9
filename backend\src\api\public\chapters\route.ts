import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { BOOK_MODULE } from "../../../modules/book"

export async function GET(req: MedusaRequest, res: MedusaResponse) {

  try {
    const bookService = req.scope.resolve(BOOK_MODULE)
    
    // Return mock data for now since we're having issues with the ORM
    const mockChapters = [
      {
        id: "chapter_1",
        title: "פרק ראשון - מבוא לעברית",
        title_en: "Chapter 1 - Introduction to Hebrew",
        title_ru: "Глава 1 - Введение в иврит",
        preview_content: "ברוכים הבאים לפרק הראשון בלימוד עברית! בפרק זה נלמד על האלפבית העברי, צלילים בסיסיים ומילים ראשונות...",
        preview_content_en: "Welcome to the first chapter in learning Hebrew! In this chapter we will learn about the Hebrew alphabet, basic sounds and first words...",
        preview_content_ru: "Добро пожаловать в первую главу изучения иврита! В этой главе мы изучим еврейский алфавит, основные звуки и первые слова...",
        difficulty_level: "beginner",
        order_in_book: 1,
        language: "he",
        price: 9.99,
        is_free: true,
        reading_time_minutes: 15,
        tags: ["אלפבית", "בסיסי", "מתחילים"],
        tags_en: ["alphabet", "basic", "beginners"],
        tags_ru: ["алфавит", "базовый", "начинающие"],
        created_at: new Date().toISOString()
      },
      {
        id: "chapter_2",
        title: "פרק שני - מילים בסיסיות",
        title_en: "Chapter 2 - Basic Vocabulary",
        title_ru: "Глава 2 - Базовая лексика",
        preview_content: "בפרק זה נרחיב את אוצר המילים שלנו ונלמד מילים בסיסיות על משפחה, צבעים ומספרים...",
        preview_content_en: "In this chapter we will expand our vocabulary and learn basic words about family, colors and numbers...",
        preview_content_ru: "В этой главе мы расширим наш словарный запас и изучим основные слова о семье, цветах и числах...",
        difficulty_level: "beginner",
        order_in_book: 2,
        language: "he",
        price: 12.99,
        is_free: false,
        reading_time_minutes: 20,
        tags: ["מילים", "משפחה", "צבעים", "מספרים"],
        tags_en: ["vocabulary", "family", "colors", "numbers"],
        tags_ru: ["словарь", "семья", "цвета", "числа"],
        created_at: new Date().toISOString()
      },
      {
        id: "chapter_3",
        title: "פרק שלישי - משפטים ראשונים",
        title_en: "Chapter 3 - First Sentences",
        title_ru: "Глава 3 - Первые предложения",
        preview_content: "עכשיו שאנחנו יודעים מילים בסיסיות, בואו נלמד לבנות משפטים ראשונים...",
        preview_content_en: "Now that we know basic words, let's learn to build first sentences...",
        preview_content_ru: "Теперь, когда мы знаем основные слова, давайте научимся строить первые предложения...",
        difficulty_level: "intermediate",
        order_in_book: 3,
        language: "he",
        price: 15.99,
        is_free: false,
        reading_time_minutes: 25,
        tags: ["משפטים", "דקדוק", "תרגילים"],
        tags_en: ["sentences", "grammar", "exercises"],
        tags_ru: ["предложения", "грамматика", "упражнения"],
        created_at: new Date().toISOString()
      }
    ]

    const {
      limit = 20,
      offset = 0,
      difficulty_level,
      language = "he",
      is_free
    } = req.query

    let filteredChapters = mockChapters

    // Apply filters
    if (difficulty_level) {
      filteredChapters = filteredChapters.filter(c => c.difficulty_level === difficulty_level)
    }

    if (language) {
      filteredChapters = filteredChapters.filter(c => c.language === language)
    }

    if (is_free !== undefined) {
      filteredChapters = filteredChapters.filter(c => c.is_free === (is_free === "true"))
    }

    // Apply pagination
    const startIndex = parseInt(offset as string)
    const endIndex = startIndex + parseInt(limit as string)
    const paginatedChapters = filteredChapters.slice(startIndex, endIndex)

    res.json({
      chapters: paginatedChapters,
      count: filteredChapters.length,
      offset: startIndex,
      limit: parseInt(limit as string),
      message: "Hebrew Book Store - Chapters API working with mock data"
    })
  } catch (error) {
    console.error("Error fetching chapters:", error)
    res.status(500).json({ 
      message: "Internal server error",
      error: error.message
    })
  }
}


