import type { Product } from '$lib/types/products'

// No fallback products - all data comes from Medusa.js API
export const products: Product[] = []


export const topics = [
  'Hebrew Basics',
  'Hebrew Alphabet', 
  'Pronunciation',
  'Vocabulary',
  'Grammar',
  'Conversations',
  'Daily Life',
  'Language History',
  'Sentence Structure',
  'Daily Communication'
]

export const productTypes = [
  { value: 'chapter', label: 'Individual Chapters' },
  { value: 'subscription', label: 'Subscriptions' },
  { value: 'bundle', label: 'Bundles' }
]

export const difficultyLevels = [
  { value: 'beginner', label: 'Beginner' },
  { value: 'intermediate', label: 'Intermediate' },
  { value: 'advanced', label: 'Advanced' }
]
