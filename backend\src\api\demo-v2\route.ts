import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const html = `
<!DOCTYPE html>
<html lang="en" dir="ltr" id="html-root">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hebrew Book Store - Advanced Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            direction: ltr;
            transition: all 0.3s ease;
        }
        
        body.rtl {
            direction: rtl;
        }
        
        .language-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: white;
            border-radius: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 5px;
            display: flex;
            gap: 5px;
        }
        
        .lang-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            background: transparent;
            color: #666;
        }
        
        .lang-btn.active {
            background: #4299e1;
            color: white;
        }
        
        .lang-btn:hover {
            background: #e2e8f0;
        }
        
        .lang-btn.active:hover {
            background: #3182ce;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .feature-card h3 {
            margin-top: 0;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .chapter-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            background: white;
            transition: all 0.3s ease;
        }
        
        .chapter-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .chapter-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 10px;
        }
        
        .chapter-meta {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        
        .chapter-preview {
            color: #4a5568;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .price-tag {
            background: #48bb78;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            display: inline-block;
        }
        
        .free-tag {
            background: #ed8936;
        }
        
        .test-button {
            background: #4299e1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 3px;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: #3182ce;
            transform: translateY(-1px);
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .search-box {
            width: 100%;
            max-width: 400px;
            padding: 12px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .search-box:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <!-- Language Switcher -->
    <div class="language-switcher">
        <button class="lang-btn active" onclick="switchLanguage('en')" data-lang="en">🇺🇸 EN</button>
        <button class="lang-btn" onclick="switchLanguage('he')" data-lang="he">🇮🇱 עב</button>
        <button class="lang-btn" onclick="switchLanguage('ru')" data-lang="ru">🇷🇺 РУ</button>
    </div>
    
    <div class="container">
        <div class="header">
            <h1 id="main-title">📚 Hebrew Book Store</h1>
            <p id="main-subtitle">Advanced Hebrew Learning Platform</p>
            <p id="main-description">Interactive e-commerce platform for Hebrew language learning</p>
        </div>
        
        <div class="content">
            <!-- Platform Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 id="stat-chapters-title">📚 Total Chapters</h3>
                    <div class="stat-number" id="total-chapters">25</div>
                </div>
                <div class="stat-card">
                    <h3 id="stat-users-title">👥 Active Users</h3>
                    <div class="stat-number" id="active-users">1,247</div>
                </div>
                <div class="stat-card">
                    <h3 id="stat-languages-title">🌍 Languages</h3>
                    <div class="stat-number" id="supported-languages">3</div>
                </div>
            </div>
            
            <!-- Features Grid -->
            <div class="feature-grid">
                <div class="feature-card">
                    <h3><span>🔍</span><span id="feature-search-title">Interactive Search</span></h3>
                    <p id="feature-search-desc">Search through Hebrew content with real-time results</p>
                    <input type="text" class="search-box" id="search-input" placeholder="Search chapters, vocabulary...">
                    <button class="test-button" onclick="performSearch()" id="search-btn">Search</button>
                    <div id="search-results" style="margin-top: 15px;"></div>
                </div>
                
                <div class="feature-card">
                    <h3><span>📖</span><span id="feature-chapters-title">Chapter Management</span></h3>
                    <p id="feature-chapters-desc">Browse and access Hebrew learning chapters</p>
                    <button class="test-button" onclick="loadChapters()" id="load-chapters-btn">Load Chapters</button>
                    <button class="test-button" onclick="loadFreeChapters()" id="load-free-btn">Free Only</button>
                    <div id="chapters-display" style="margin-top: 15px;"></div>
                </div>
                
                <div class="feature-card">
                    <h3><span>💳</span><span id="feature-subscription-title">Subscription Plans</span></h3>
                    <p id="feature-subscription-desc">Flexible subscription options for learners</p>
                    <button class="test-button" onclick="loadSubscriptions()" id="load-subs-btn">View Plans</button>
                    <div id="subscription-display" style="margin-top: 15px;"></div>
                </div>
                
                <div class="feature-card">
                    <h3><span>📊</span><span id="feature-analytics-title">Platform Analytics</span></h3>
                    <p id="feature-analytics-desc">Real-time platform statistics and insights</p>
                    <button class="test-button" onclick="loadAnalytics()" id="load-analytics-btn">View Analytics</button>
                    <div id="analytics-display" style="margin-top: 15px;"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentLanguage = 'en';
        let translations = {};
        
        // Load translations from API
        async function loadTranslations(lang) {
            try {
                const response = await fetch(\`/public/translations?language=\${lang}\`);
                const data = await response.json();
                translations[lang] = data.translations;
                return data.translations;
            } catch (error) {
                console.error('Error loading translations:', error);
                return {};
            }
        }
        
        // Apply translations to the page
        function applyTranslations(lang) {
            const t = translations[lang] || {};
            
            // Update main titles
            document.getElementById('main-title').textContent = 
                lang === 'he' ? '📚 חנות ספרי עברית' : 
                lang === 'ru' ? '📚 Магазин книг на иврите' : 
                '📚 Hebrew Book Store';
                
            document.getElementById('main-subtitle').textContent = 
                lang === 'he' ? 'פלטפורמת למידת עברית מתקדמת' : 
                lang === 'ru' ? 'Продвинутая платформа изучения иврита' : 
                'Advanced Hebrew Learning Platform';
                
            document.getElementById('main-description').textContent = 
                lang === 'he' ? 'פלטפורמת מסחר אלקטרוני אינטראקטיבית ללימוד עברית' : 
                lang === 'ru' ? 'Интерактивная платформа электронной коммерции для изучения иврита' : 
                'Interactive e-commerce platform for Hebrew language learning';
            
            // Update feature titles
            document.getElementById('feature-search-title').textContent = 
                lang === 'he' ? 'חיפוש אינטראקטיבי' : 
                lang === 'ru' ? 'Интерактивный поиск' : 
                'Interactive Search';
                
            document.getElementById('feature-chapters-title').textContent = 
                lang === 'he' ? 'ניהול פרקים' : 
                lang === 'ru' ? 'Управление главами' : 
                'Chapter Management';
                
            document.getElementById('feature-subscription-title').textContent = 
                lang === 'he' ? 'תוכניות מנוי' : 
                lang === 'ru' ? 'Планы подписки' : 
                'Subscription Plans';
                
            document.getElementById('feature-analytics-title').textContent = 
                lang === 'he' ? 'אנליטיקת פלטפורמה' : 
                lang === 'ru' ? 'Аналитика платформы' : 
                'Platform Analytics';
            
            // Update buttons
            document.getElementById('search-btn').textContent = 
                lang === 'he' ? 'חיפוש' : 
                lang === 'ru' ? 'Поиск' : 
                'Search';
                
            document.getElementById('load-chapters-btn').textContent = 
                lang === 'he' ? 'טעינת פרקים' : 
                lang === 'ru' ? 'Загрузить главы' : 
                'Load Chapters';
                
            document.getElementById('load-free-btn').textContent = 
                lang === 'he' ? 'חינם בלבד' : 
                lang === 'ru' ? 'Только бесплатные' : 
                'Free Only';
            
            // Update search placeholder
            document.getElementById('search-input').placeholder = 
                lang === 'he' ? 'חיפוש פרקים, אוצר מילים...' : 
                lang === 'ru' ? 'Поиск глав, словаря...' : 
                'Search chapters, vocabulary...';
        }
        
        async function switchLanguage(lang) {
            currentLanguage = lang;
            
            // Update HTML direction
            const htmlRoot = document.getElementById('html-root');
            const body = document.body;
            
            if (lang === 'he') {
                htmlRoot.setAttribute('dir', 'rtl');
                htmlRoot.setAttribute('lang', 'he');
                body.classList.add('rtl');
            } else {
                htmlRoot.setAttribute('dir', 'ltr');
                htmlRoot.setAttribute('lang', lang);
                body.classList.remove('rtl');
            }
            
            // Update active language button
            document.querySelectorAll('.lang-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.getAttribute('data-lang') === lang) {
                    btn.classList.add('active');
                }
            });
            
            // Load and apply translations
            if (!translations[lang]) {
                await loadTranslations(lang);
            }
            applyTranslations(lang);
            
            // Store language preference
            localStorage.setItem('preferred-language', lang);
        }
        
        async function performSearch() {
            const query = document.getElementById('search-input').value.trim();
            const resultsContainer = document.getElementById('search-results');
            
            if (!query) {
                resultsContainer.innerHTML = '<p style="color: #666;">Please enter a search term</p>';
                return;
            }
            
            resultsContainer.innerHTML = '<div class="loading">Searching...</div>';
            
            try {
                const response = await fetch(\`/public/search?q=\${encodeURIComponent(query)}\`);
                const data = await response.json();
                
                if (data.results && data.results.length > 0) {
                    resultsContainer.innerHTML = \`
                        <div style="font-size: 0.9em; color: #666; margin-bottom: 10px;">
                            Found \${data.total_found} results (\${data.search_time_ms}ms)
                        </div>
                        \${data.results.slice(0, 3).map(result => \`
                            <div style="border: 1px solid #e0e0e0; border-radius: 5px; padding: 10px; margin: 5px 0; font-size: 0.8em;">
                                <strong>\${result.title}</strong><br>
                                <span style="color: #666;">\${result.type} • \${Math.round(result.relevance_score * 100)}% match</span><br>
                                <span style="color: #888;">\${result.search_snippet}</span>
                            </div>
                        \`).join('')}
                    \`;
                } else {
                    resultsContainer.innerHTML = '<p style="color: #666;">No results found</p>';
                }
            } catch (error) {
                resultsContainer.innerHTML = '<p style="color: #e53e3e;">Search error: ' + error.message + '</p>';
            }
        }
        
        async function loadChapters() {
            const container = document.getElementById('chapters-display');
            container.innerHTML = '<div class="loading">Loading chapters...</div>';
            
            try {
                const response = await fetch('/public/chapters');
                const data = await response.json();
                
                container.innerHTML = data.chapters.slice(0, 2).map(chapter => {
                    const title = currentLanguage === 'he' ? chapter.title : 
                                 currentLanguage === 'ru' ? chapter.title_ru : 
                                 chapter.title_en;
                    const preview = currentLanguage === 'he' ? chapter.preview_content : 
                                   currentLanguage === 'ru' ? chapter.preview_content_ru : 
                                   chapter.preview_content_en;
                    
                    return \`
                        <div style="border: 1px solid #e0e0e0; border-radius: 5px; padding: 10px; margin: 5px 0; font-size: 0.8em;">
                            <strong>\${title}</strong><br>
                            <span style="color: #666;">\${chapter.difficulty_level} • \${chapter.reading_time_minutes} min</span><br>
                            <span style="color: #888;">\${preview.substring(0, 100)}...</span><br>
                            <span class="price-tag \${chapter.is_free ? 'free-tag' : ''}" style="margin-top: 5px;">
                                \${chapter.is_free ? 'Free' : '$' + chapter.price}
                            </span>
                        </div>
                    \`;
                }).join('');
            } catch (error) {
                container.innerHTML = '<p style="color: #e53e3e;">Error loading chapters</p>';
            }
        }
        
        async function loadFreeChapters() {
            const container = document.getElementById('chapters-display');
            container.innerHTML = '<div class="loading">Loading free chapters...</div>';
            
            try {
                const response = await fetch('/public/chapters?is_free=true');
                const data = await response.json();
                
                container.innerHTML = data.chapters.map(chapter => {
                    const title = currentLanguage === 'he' ? chapter.title : 
                                 currentLanguage === 'ru' ? chapter.title_ru : 
                                 chapter.title_en;
                    
                    return \`
                        <div style="border: 1px solid #e0e0e0; border-radius: 5px; padding: 10px; margin: 5px 0; font-size: 0.8em;">
                            <strong>\${title}</strong><br>
                            <span style="color: #666;">\${chapter.difficulty_level} • Free</span>
                        </div>
                    \`;
                }).join('');
            } catch (error) {
                container.innerHTML = '<p style="color: #e53e3e;">Error loading chapters</p>';
            }
        }
        
        async function loadSubscriptions() {
            const container = document.getElementById('subscription-display');
            container.innerHTML = '<div class="loading">Loading plans...</div>';
            
            try {
                const response = await fetch('/public/subscriptions/plans');
                const data = await response.json();
                
                container.innerHTML = data.plans.map(plan => \`
                    <div style="border: 1px solid #e0e0e0; border-radius: 5px; padding: 10px; margin: 5px 0; font-size: 0.8em;">
                        <strong>\${currentLanguage === 'he' ? plan.name : currentLanguage === 'ru' ? plan.name_en.replace('Subscription', 'Подписка') : plan.name_en}</strong><br>
                        <span style="color: #4299e1; font-weight: bold;">$\${plan.price}</span> / \${plan.duration_months} months<br>
                        \${plan.popular ? '<span style="color: #ed8936;">Most Popular!</span>' : ''}
                    </div>
                \`).join('');
            } catch (error) {
                container.innerHTML = '<p style="color: #e53e3e;">Error loading plans</p>';
            }
        }
        
        async function loadAnalytics() {
            const container = document.getElementById('analytics-display');
            container.innerHTML = '<div class="loading">Loading analytics...</div>';
            
            try {
                const response = await fetch('/public/stats');
                const data = await response.json();
                const stats = data.stats;
                
                container.innerHTML = \`
                    <div style="font-size: 0.8em;">
                        <div style="margin: 5px 0;"><strong>Total Users:</strong> \${stats.platform.total_users.toLocaleString()}</div>
                        <div style="margin: 5px 0;"><strong>Reading Hours:</strong> \${stats.platform.total_reading_hours.toLocaleString()}</div>
                        <div style="margin: 5px 0;"><strong>Top Chapter:</strong> \${stats.popular_chapters[0].title}</div>
                        <div style="margin: 5px 0;"><strong>Avg Rating:</strong> \${stats.popular_chapters[0].average_rating}/5</div>
                    </div>
                \`;
            } catch (error) {
                container.innerHTML = '<p style="color: #e53e3e;">Error loading analytics</p>';
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', async function() {
            const savedLang = localStorage.getItem('preferred-language') || 'en';
            await switchLanguage(savedLang);
            
            // Add Enter key support for search
            document.getElementById('search-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        });
    </script>
</body>
</html>
  `;

  res.setHeader('Content-Type', 'text/html; charset=utf-8');
  res.send(html);
}
