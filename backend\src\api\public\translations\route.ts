import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { language = "en" } = req.query

    // Mock translations for the platform
    const translations = {
      en: {
        // Navigation
        "nav.chapters": "Chapters",
        "nav.search": "Search",
        "nav.subscriptions": "Subscriptions",
        "nav.about": "About",
        
        // Common
        "common.loading": "Loading...",
        "common.error": "Error",
        "common.success": "Success",
        "common.cancel": "Cancel",
        "common.save": "Save",
        "common.delete": "Delete",
        "common.edit": "Edit",
        "common.view": "View",
        "common.back": "Back",
        "common.next": "Next",
        "common.previous": "Previous",
        
        // Chapter related
        "chapter.title": "Chapter Title",
        "chapter.content": "Content",
        "chapter.preview": "Preview",
        "chapter.difficulty": "Difficulty Level",
        "chapter.price": "Price",
        "chapter.free": "Free",
        "chapter.paid": "Paid",
        "chapter.reading_time": "Reading Time",
        "chapter.tags": "Tags",
        "chapter.purchase": "Purchase",
        "chapter.read": "Read",
        
        // Difficulty levels
        "difficulty.beginner": "Beginner",
        "difficulty.intermediate": "Intermediate", 
        "difficulty.advanced": "Advanced",
        
        // Subscription
        "subscription.plans": "Subscription Plans",
        "subscription.monthly": "Monthly",
        "subscription.yearly": "Yearly",
        "subscription.features": "Features",
        "subscription.popular": "Most Popular",
        "subscription.savings": "Savings",
        "subscription.subscribe": "Subscribe",
        
        // Search
        "search.placeholder": "Search chapters, vocabulary...",
        "search.results": "Search Results",
        "search.no_results": "No results found",
        "search.suggestions": "Suggestions",
        
        // Analytics
        "analytics.revenue": "Revenue",
        "analytics.users": "Users",
        "analytics.chapters": "Chapters",
        "analytics.growth": "Growth",
        "analytics.completion_rate": "Completion Rate",
        "analytics.engagement": "Engagement",
        
        // Admin
        "admin.dashboard": "Admin Dashboard",
        "admin.manage_chapters": "Manage Chapters",
        "admin.analytics": "Analytics",
        "admin.users": "Users",
        "admin.settings": "Settings"
      },
      
      he: {
        // Navigation
        "nav.chapters": "פרקים",
        "nav.search": "חיפוש",
        "nav.subscriptions": "מנויים",
        "nav.about": "אודות",
        
        // Common
        "common.loading": "טוען...",
        "common.error": "שגיאה",
        "common.success": "הצלחה",
        "common.cancel": "ביטול",
        "common.save": "שמירה",
        "common.delete": "מחיקה",
        "common.edit": "עריכה",
        "common.view": "צפייה",
        "common.back": "חזרה",
        "common.next": "הבא",
        "common.previous": "הקודם",
        
        // Chapter related
        "chapter.title": "כותרת הפרק",
        "chapter.content": "תוכן",
        "chapter.preview": "תצוגה מקדימה",
        "chapter.difficulty": "רמת קושי",
        "chapter.price": "מחיר",
        "chapter.free": "חינם",
        "chapter.paid": "בתשלום",
        "chapter.reading_time": "זמן קריאה",
        "chapter.tags": "תגיות",
        "chapter.purchase": "רכישה",
        "chapter.read": "קריאה",
        
        // Difficulty levels
        "difficulty.beginner": "מתחילים",
        "difficulty.intermediate": "בינוני",
        "difficulty.advanced": "מתקדמים",
        
        // Subscription
        "subscription.plans": "תוכניות מנוי",
        "subscription.monthly": "חודשי",
        "subscription.yearly": "שנתי",
        "subscription.features": "תכונות",
        "subscription.popular": "הכי פופולרי",
        "subscription.savings": "חיסכון",
        "subscription.subscribe": "הרשמה למנוי",
        
        // Search
        "search.placeholder": "חיפוש פרקים, אוצר מילים...",
        "search.results": "תוצאות חיפוש",
        "search.no_results": "לא נמצאו תוצאות",
        "search.suggestions": "הצעות",
        
        // Analytics
        "analytics.revenue": "הכנסות",
        "analytics.users": "משתמשים",
        "analytics.chapters": "פרקים",
        "analytics.growth": "צמיחה",
        "analytics.completion_rate": "שיעור השלמה",
        "analytics.engagement": "מעורבות",
        
        // Admin
        "admin.dashboard": "לוח בקרה",
        "admin.manage_chapters": "ניהול פרקים",
        "admin.analytics": "אנליטיקה",
        "admin.users": "משתמשים",
        "admin.settings": "הגדרות"
      },
      
      ru: {
        // Navigation
        "nav.chapters": "Главы",
        "nav.search": "Поиск",
        "nav.subscriptions": "Подписки",
        "nav.about": "О нас",
        
        // Common
        "common.loading": "Загрузка...",
        "common.error": "Ошибка",
        "common.success": "Успех",
        "common.cancel": "Отмена",
        "common.save": "Сохранить",
        "common.delete": "Удалить",
        "common.edit": "Редактировать",
        "common.view": "Просмотр",
        "common.back": "Назад",
        "common.next": "Далее",
        "common.previous": "Предыдущий",
        
        // Chapter related
        "chapter.title": "Название главы",
        "chapter.content": "Содержание",
        "chapter.preview": "Предварительный просмотр",
        "chapter.difficulty": "Уровень сложности",
        "chapter.price": "Цена",
        "chapter.free": "Бесплатно",
        "chapter.paid": "Платно",
        "chapter.reading_time": "Время чтения",
        "chapter.tags": "Теги",
        "chapter.purchase": "Купить",
        "chapter.read": "Читать",
        
        // Difficulty levels
        "difficulty.beginner": "Начинающий",
        "difficulty.intermediate": "Средний",
        "difficulty.advanced": "Продвинутый",
        
        // Subscription
        "subscription.plans": "Планы подписки",
        "subscription.monthly": "Месячный",
        "subscription.yearly": "Годовой",
        "subscription.features": "Возможности",
        "subscription.popular": "Самый популярный",
        "subscription.savings": "Экономия",
        "subscription.subscribe": "Подписаться",
        
        // Search
        "search.placeholder": "Поиск глав, словаря...",
        "search.results": "Результаты поиска",
        "search.no_results": "Результаты не найдены",
        "search.suggestions": "Предложения",
        
        // Analytics
        "analytics.revenue": "Доходы",
        "analytics.users": "Пользователи",
        "analytics.chapters": "Главы",
        "analytics.growth": "Рост",
        "analytics.completion_rate": "Процент завершения",
        "analytics.engagement": "Вовлеченность",
        
        // Admin
        "admin.dashboard": "Панель администратора",
        "admin.manage_chapters": "Управление главами",
        "admin.analytics": "Аналитика",
        "admin.users": "Пользователи",
        "admin.settings": "Настройки"
      }
    }

    const selectedTranslations = translations[language as string] || translations.en

    res.json({
      language,
      translations: selectedTranslations,
      available_languages: Object.keys(translations),
      message: `Translations for ${language} language`
    })
  } catch (error) {
    console.error("Error fetching translations:", error)
    res.status(500).json({ 
      message: "Internal server error",
      error: error.message
    })
  }
}
