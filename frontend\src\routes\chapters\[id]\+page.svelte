<script lang="ts">
	import { onMount } from 'svelte'
	import { page } from '$app/stores'
	import { _ } from '$lib/i18n'
	import { getCurrentLocale, isRTL } from '$lib/i18n'
	import { chaptersStore } from '$lib/stores/chapters'
	import type { Chapter } from '$lib/api/client'

	let currentLocale = 'en'
	let rtl = false
	let chapter: Chapter | null = null
	let isLoading = false
	let error: string | null = null

	// Subscribe to chapters store
	$: ({ currentChapter: chapter, isLoading, error } = $chaptersStore)
	$: currentLocale = getCurrentLocale()
	$: rtl = isRTL(currentLocale)
	$: chapterId = $page.params.id

	onMount(async () => {
		if (chapterId) {
			await chaptersStore.loadChapter(chapterId)
		}
	})



	function getLocalizedTitle(chapter: Chapter, locale: string): string {
		switch (locale) {
			case 'he':
				return chapter.title
			case 'en':
				return chapter.title_en || chapter.title
			case 'ru':
				return chapter.title_ru || chapter.title
			default:
				return chapter.title
		}
	}

	function getLocalizedDescription(chapter: Chapter, locale: string): string {
		switch (locale) {
			case 'he':
				return chapter.preview_content || chapter.description || ''
			case 'en':
				return chapter.preview_content_en || chapter.description_en || chapter.description || ''
			case 'ru':
				return chapter.preview_content_ru || chapter.description || ''
			default:
				return chapter.preview_content || chapter.description || ''
		}
	}

	$: title = chapter ? getLocalizedTitle(chapter, currentLocale) : ''
	$: description = chapter ? getLocalizedDescription(chapter, currentLocale) : ''
</script>

<svelte:head>
	<title>{title || $_('chapters.chapter')} - Hebrew Book Store</title>
	<meta name="description" content={description || 'Hebrew learning chapter'} />
</svelte:head>

<div class="min-h-screen bg-gray-50" class:rtl>
	<!-- Breadcrumb -->
	<div class="bg-white border-b border-gray-200">
		<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
			<nav class="flex items-center space-x-2 text-sm text-gray-500" class:space-x-reverse={rtl}>
				<a href="/" class="hover:text-blue-600">{$_('navigation.home')}</a>
				<span>/</span>
				<a href="/chapters" class="hover:text-blue-600">{$_('navigation.chapters')}</a>
				<span>/</span>
				<span class="text-gray-900">{title || $_('chapters.chapter')}</span>
			</nav>
		</div>
	</div>

	<!-- Main Content -->
	<main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
		{#if isLoading}
			<div class="flex justify-center items-center py-12">
				<div class="text-center">
					<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
					<p class="text-gray-600">{$_('common.loading')}</p>
				</div>
			</div>
		{:else if error}
			<div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
				<div class="flex">
					<div class="flex-shrink-0">
						<svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
							<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
						</svg>
					</div>
					<div class="ml-3" class:mr-3={rtl} class:ml-0={rtl}>
						<h3 class="text-sm font-medium text-red-800">
							{$_('common.error')}
						</h3>
						<div class="mt-2 text-sm text-red-700">
							<p>{error}</p>
						</div>
						<div class="mt-4">
							<button
								type="button"
								class="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
								on:click={() => chaptersStore.loadChapter(chapterId)}
							>
								{$_('common.retry')}
							</button>
						</div>
					</div>
				</div>
			</div>
		{:else if !chapter}
			<div class="text-center py-12">
				<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
				</svg>
				<h3 class="mt-2 text-sm font-medium text-gray-900">Chapter not found</h3>
				<p class="mt-1 text-sm text-gray-500">The requested chapter could not be found.</p>
			</div>
		{:else}
			<!-- Chapter Content -->
			<article class="bg-white rounded-lg shadow-md overflow-hidden">
				<!-- Chapter Header -->
				<div class="px-6 py-8 border-b border-gray-200">
					<div class="flex items-start justify-between mb-4">
						<div class="flex items-center space-x-3" class:space-x-reverse={rtl}>
							<span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
								{$_('chapters.chapter')} {chapter.chapter_number || chapter.order_in_book}
							</span>
							
							{#if chapter.difficulty_level}
								<span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
									{$_(`chapters.${chapter.difficulty_level}`)}
								</span>
							{/if}
							
							{#if chapter.is_free}
								<span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
									{$_('common.free')}
								</span>
							{/if}
						</div>
						
						{#if chapter.estimated_reading_time || chapter.reading_time_minutes}
							<span class="text-sm text-gray-500 flex items-center">
								<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
								</svg>
								{chapter.estimated_reading_time || chapter.reading_time_minutes} {$_('chapters.minutes')}
							</span>
						{/if}
					</div>

					<h1 class="text-3xl font-bold text-gray-900 mb-4" class:text-right={rtl}>
						{title}
					</h1>

					{#if description}
						<p class="text-lg text-gray-600 mb-6" class:text-right={rtl}>
							{description}
						</p>
					{/if}

					<!-- Media Links -->
					{#if chapter.video_url || chapter.audio_url || chapter.exercise_url}
						<div class="flex items-center space-x-4" class:space-x-reverse={rtl}>
							{#if chapter.video_url}
								<a 
									href={chapter.video_url}
									target="_blank"
									rel="noopener noreferrer"
									class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
								>
									<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
									</svg>
									Watch Video
								</a>
							{/if}
							
							{#if chapter.audio_url}
								<a 
									href={chapter.audio_url}
									target="_blank"
									rel="noopener noreferrer"
									class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
								>
									<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.816L4.846 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.846l3.537-3.816a1 1 0 011.617.816z" clip-rule="evenodd" />
									</svg>
									Listen Audio
								</a>
							{/if}
							
							{#if chapter.exercise_url}
								<a 
									href={chapter.exercise_url}
									target="_blank"
									rel="noopener noreferrer"
									class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
								>
									<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8z" clip-rule="evenodd" />
									</svg>
									Practice Exercises
								</a>
							{/if}
						</div>
					{/if}
				</div>

				<!-- Chapter Content -->
				<div class="px-6 py-8">
					{#if chapter.content}
						<div class="prose max-w-none" class:prose-rtl={rtl}>
							{@html chapter.content}
						</div>
					{:else}
						<p class="text-gray-500 italic" class:text-right={rtl}>
							Chapter content will be available soon.
						</p>
					{/if}
				</div>
			</article>
		{/if}
	</main>
</div>

<style>
	:global(.prose-rtl) {
		direction: rtl;
		text-align: right;
	}
</style>
