import { json } from '@sveltejs/kit'
import type { RequestHand<PERSON> } from './$types'
import { MEDUSA_BACKEND_URL } from '$env/static/private'

export const GET: RequestHandler = async ({ request, fetch }) => {
  try {
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return json(
        { message: 'Authorization token required' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7) // Remove 'Bearer ' prefix

    // Get customer data from Medusa.js
    const customerResponse = await fetch(`${MEDUSA_BACKEND_URL}/store/customers/me`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    })

    if (!customerResponse.ok) {
      return json(
        { message: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    const customerData = await customerResponse.json()

    return json({
      success: true,
      customer: customerData.customer
    })

  } catch (error) {
    console.error('Auth check error:', error)
    return json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
