# Hebrew Book Store - Руководство по тестированию

## 🎯 Обзор тестовой среды

Проект настроен для работы в двух режимах:
- **API режим**: Полная интеграция с Medusa.js + PostgreSQL
- **Fallback режим**: Автономная работа без бэкенда

## 🗄️ Инфраструктура

### PostgreSQL Database
- **Контейнер**: `hebrew-book-store-postgres`
- **Порт**: `5432`
- **База данных**: `hebrew_book_clean`
- **Пользователь**: `medusa_user`
- **Пароль**: `medusa_password`
- **Статус**: ✅ Запущена и готова

### Redis Cache
- **Контейнер**: `hebrew-book-store-redis`
- **Порт**: `6379`
- **Статус**: ✅ Запущен

### Medusa.js Backend
- **Порт**: `9000`
- **URL**: `http://localhost:9000`
- **Health Check**: `http://localhost:9000/health`
- **Статус**: ✅ Запущен
- **Таблицы**: 133 таблицы созданы

## 🔧 Режимы работы

### 📦 Fallback режим (текущий)
**Описание**: Полностью автономная работа без зависимости от бэкенда

**Особенности**:
- ✅ Локальная аутентификация
- ✅ Статические данные продуктов
- ✅ CRUD операции в localStorage
- ✅ Полная функциональность интерфейса
- ✅ Синхронизация между админкой и каталогом

**Индикатор**: Синяя панель "Development Mode" на странице логина

### 🟢 API режим (требует настройки)
**Описание**: Полная интеграция с Medusa.js и PostgreSQL

**Требует**:
- Publishable API key
- Админского пользователя в базе
- Настройку аутентификации

## 🧪 Как тестировать

### 1️⃣ Админская панель (Medusa.js)

**URL**: `http://localhost:9000/app`

**Учетные данные**:
```
Email: <EMAIL>
Password: admin123
```

**Функции для тестирования**:
- ✅ Вход в систему
- ✅ Создание продуктов
- ✅ Редактирование продуктов
- ✅ Удаление продуктов
- ✅ Управление заказами
- ✅ Настройка API ключей
- ✅ Управление клиентами

**Примечание**: Используется только официальная Medusa.js админка. Кастомная Svelte админка удалена.

### 2️⃣ Каталог товаров

**URL**: `http://localhost:5173/chapters`

**Функции для тестирования**:
- ✅ Просмотр продуктов
- ✅ Поиск по названию/описанию
- ✅ Фильтрация по типу/сложности/темам
- ✅ Добавление в корзину
- ✅ Переключение вида (сетка/список)
- ✅ Локализация интерфейса

### 3️⃣ Синхронизация данных

**Тест**:
1. Откройте админку: `http://localhost:9000/app`
2. Откройте каталог в новой вкладке: `http://localhost:5173/chapters`
3. Создайте новый продукт в админке
4. Убедитесь, что он появился в каталоге (после обновления страницы)
5. Измените статус публикации
6. Проверьте изменения в каталоге

## 🔄 Архитектура синхронизации

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Admin Panel   │    │   Products Store │    │    Catalog      │
│                 │◄──►│                  │◄──►│                 │
│ CRUD Operations │    │  Svelte Store    │    │ Display & Filter│
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │   localStorage   │
                    │   (Fallback)     │
                    └──────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │  Medusa.js API   │
                    │   (When ready)   │
                    └──────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │   PostgreSQL     │
                    │   (Database)     │
                    └──────────────────┘
```

## 🌍 Локализация

### Поддерживаемые языки
- 🇺🇸 **Английский** (по умолчанию)
- 🇷🇺 **Русский**

### Переключение языка
- Используйте переключатель в навигации
- Все тексты переводятся автоматически
- Состояние сохраняется в localStorage

## 🚀 Настройка администратора

### Создание первого администратора

**Важно**: Используется только официальная Medusa.js админка (`http://localhost:9000/app` или `http://your-domain.com:9000/app`). Кастомная Svelte админка удалена.

#### Локальная разработка:

1. **Через CLI (рекомендуется)**:
```bash
cd backend
yarn medusa user -e <EMAIL> -p admin123
```

2. **Альтернативный способ через npx**:
```bash
cd backend
npx medusa user -e <EMAIL> -p admin123
```

#### Продакшен развертывание:

1. **После развертывания на сервере**:
```bash
# SSH на сервер
ssh <EMAIL>

# Перейти в директорию проекта
cd /path/to/hebrew-book-store

# Создать админа
yarn medusa user -e <EMAIL> -p your-secure-password
```

2. **Через Docker (если используется)**:
```bash
# Выполнить команду в контейнере
docker exec -it medusa-backend yarn medusa user -e <EMAIL> -p your-secure-password
```

3. **Через переменные окружения** (для автоматизации):
```bash
# Добавить в .env на сервере
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your-secure-password

# Создать скрипт инициализации
echo "yarn medusa user -e \$ADMIN_EMAIL -p \$ADMIN_PASSWORD" > init-admin.sh
chmod +x init-admin.sh
./init-admin.sh
```

### Безопасность в продакшене

1. **Используйте сильные пароли** для админских аккаунтов
2. **Ограничьте доступ** к админке по IP (если возможно)
3. **Регулярно меняйте** пароли
4. **Используйте HTTPS** для всех админских операций
5. **Настройте backup** базы данных с админскими аккаунтами

## 🔑 Настройка API ключей

### Создание Publishable API Key

**Важно**: API ключ создается **только через официальную админку**. Никаких SQL команд или кода не требуется!

#### Простые шаги:

1. **Войти в админку**:
   - Локально: `http://localhost:9000/app`
   - Продакшен: `http://your-domain.com:9000/app`

2. **Войти с учетными данными админа**

3. **Создать API ключ**:
   - Перейти в **Settings** → **API Keys**
   - Нажать **"Create API Key"**
   - Выбрать тип: **"Publishable"**
   - Указать название: **"Storefront Key"**
   - Скопировать созданный ключ

4. **Добавить ключ в переменные окружения**:

**Локально** (создать файл `.env.local` в папке frontend):
```env
VITE_MEDUSA_PUBLISHABLE_API_KEY=pk_your_actual_key_here
```

**Продакшен** (в настройках хостинга или `.env.production`):
```env
VITE_MEDUSA_PUBLISHABLE_API_KEY=pk_your_actual_key_here
```

5. **Перезапустить фронтенд** для применения изменений



### Шаг 2: Создание админского пользователя

⚠️ **ВАЖНО**: Medusa.js v2 использует **invite-based систему** для админов!

#### Правильный способ создания админа:

1. **Создать invite через Medusa CLI**:
```bash
cd backend
medusa user --email <EMAIL> --invite
```

2. **Скопировать invite token из вывода команды**:
```
Invite token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Open the invite in Medusa Admin at: [your-admin-url]/invite?token=...
```

3. **Активировать invite в браузере**:
```
http://localhost:5173/admin/invite?token=[INVITE_TOKEN]
```

4. **На странице активации**:
   - Введите пароль (например: `admin123`)
   - Подтвердите пароль
   - Нажмите "Accept Invite"

5. **После активации войти в админку**:
```
URL: http://localhost:5173/admin/login
Email: <EMAIL>
Password: admin123
```

#### ❌ Неправильные способы (не работают в v2):
- Прямое создание пользователя через SQL
- Команда `medusa user --email --password` (создает пользователя без auth identity)
- Ручное создание записей в auth_identity/provider_identity

## 📊 Статус компонентов

| Компонент | Статус | Описание |
|-----------|--------|----------|
| Frontend | ✅ Готов | Полная функциональность |
| PostgreSQL | ✅ Готов | База данных настроена |
| Medusa.js | ✅ Запущен | Сервер работает |
| API Keys | ✅ Готов | Publishable key создан и работает |
| Auth System | ✅ Готов | Invite-based система настроена |
| Store API | ✅ Работает | Интеграция с publishable key |
| Admin API | ✅ Готов | После активации invite |
| Fallback Mode | ✅ Работает | Полная автономность |

## 🔍 Отладка

### Проверка статуса сервисов
```bash
# PostgreSQL
docker ps | grep postgres

# Medusa.js
curl http://localhost:9000/health

# Frontend
curl http://localhost:5173
```

### Тестирование API
```bash
# Store API (должен работать с publishable key)
curl -H "x-publishable-api-key: pk_test_hebrew_book_store_development" http://localhost:9000/store/products

# Ожидаемый ответ: {"products":[],"count":0,"offset":0,"limit":50}
```

### Логи
- **Medusa.js**: Смотрите терминал где запущен `yarn dev`
- **Frontend**: Консоль браузера (F12)
- **PostgreSQL**: `docker logs hebrew-book-store-postgres`

## 🚨 Troubleshooting

### Проблема: "Admin Auth не работает"

**Симптомы**: Не можете войти в админку с email/password

**Решение**:
1. Убедитесь, что используете invite-based систему
2. Создайте invite: `medusa user --email <EMAIL> --invite`
3. Активируйте invite по ссылке с токеном
4. Только после активации можно войти с паролем

### Проблема: "Store API возвращает ошибку"

**Симптомы**:
```json
{"type":"invalid_data","message":"Publishable key needs to have a sales channel configured"}
```

**Решение**: Убедитесь, что:
1. API ключ создан в таблице `api_key`
2. Sales channel создан в таблице `sales_channel`
3. Связь создана в таблице `publishable_api_key_sales_channel`

### Проблема: "Medusa CLI не работает"

**Симптомы**:
```
Error: Cannot find module 'tsconfig-paths'
```

**Решение**:
```bash
npm install -g ts-node tsconfig-paths
```

### Проблема: "Пользователь уже существует"

**Симптомы**:
```
MedusaError: User with email: <EMAIL>, already exists.
```

**Решение**:
1. Удалите старого пользователя: `DELETE FROM "user" WHERE email = '<EMAIL>';`
2. Создайте новый invite: `medusa user --email <EMAIL> --invite`

## 🎉 Заключение

Тестовая среда полностью готова для разработки и тестирования:
- **Сейчас**: Полная функциональность в fallback режиме
- **Будущее**: Легкое переключение на API режим после настройки

Все функции Hebrew Book Store можно тестировать прямо сейчас! 🚀
