# Hebrew Book Store Frontend - Local Development
# This file contains your actual API keys and should NOT be committed to git

# Backend API URL
VITE_BACKEND_URL=http://localhost:9000

# Medusa.js Publishable API Key (your actual key from admin panel)
VITE_MEDUSA_PUBLISHABLE_API_KEY="pk_a00302b727fcf4f5285975080843c4bb959bc257a8a7e070ea0967c396c64505"

# App Configuration
VITE_APP_NAME=Hebrew Book Store
VITE_APP_URL=http://localhost:5173

# Development Mode
NODE_ENV=development

# Server-side Backend URL (for API routes)
MEDUSA_BACKEND_URL=http://localhost:9000
