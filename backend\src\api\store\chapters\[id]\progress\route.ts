import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { BOOK_MODULE } from "../../../../../modules/book"

// GET reading progress for a chapter
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const bookService = req.scope.resolve(BOOK_MODULE)
  const { id } = req.params
  
  if (!req.user) {
    return res.status(401).json({ message: "Authentication required" })
  }

  try {
    const progress = await bookService.getUserReadingProgress(req.user.id, id)
    
    res.json({
      progress: progress[0] || {
        user_id: req.user.id,
        chapter_id: id,
        progress_percentage: 0,
        time_spent_minutes: 0,
        bookmarked: false
      }
    })
  } catch (error) {
    console.error("Error fetching reading progress:", error)
    res.status(500).json({ message: "Internal server error" })
  }
}

// POST/PUT update reading progress
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const bookService = req.scope.resolve(BOOK_MODULE)
  const { id } = req.params
  
  if (!req.user) {
    return res.status(401).json({ message: "Authentication required" })
  }

  try {
    // Check if user has access to this chapter
    const hasAccess = await bookService.checkUserAccess(req.user.id, id)
    if (!hasAccess) {
      return res.status(403).json({ message: "Access denied" })
    }

    const {
      progress_percentage,
      last_position,
      time_spent_minutes,
      bookmarked,
      notes
    } = req.body

    const progressData: any = {}

    if (progress_percentage !== undefined) {
      progressData.progress_percentage = Math.max(0, Math.min(100, progress_percentage))
      
      // Mark as completed if 100%
      if (progressData.progress_percentage === 100) {
        progressData.completed_at = new Date()
      }
    }

    if (last_position !== undefined) {
      progressData.last_position = JSON.stringify(last_position)
    }

    if (time_spent_minutes !== undefined) {
      progressData.time_spent_minutes = Math.max(0, time_spent_minutes)
    }

    if (bookmarked !== undefined) {
      progressData.bookmarked = bookmarked
    }

    if (notes !== undefined) {
      progressData.notes = notes
    }

    const updatedProgress = await bookService.updateReadingProgress(
      req.user.id,
      id,
      progressData
    )

    res.json({ progress: updatedProgress })
  } catch (error) {
    console.error("Error updating reading progress:", error)
    res.status(500).json({ message: "Internal server error" })
  }
}
