import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { BOOK_MODULE } from "../../../../modules/book"

// GET - Get specific chapter for admin
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params
  
  try {
    const bookService = req.scope.resolve(BOOK_MODULE)
    
    // Mock detailed chapter data
    const mockChapters = {
      "chapter_1": {
        id: "chapter_1",
        title: "פרק ראשון - מבוא לעברית",
        content: `
          <div dir="rtl" style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h1>מבוא לעברית</h1>
            <p>ברוכים הבאים לפרק הראשון בלימוד עברית!</p>
            <p>בפרק זה נלמד על:</p>
            <ul>
              <li>האלפבית העברי</li>
              <li>צלילים בסיסיים</li>
              <li>מילים ראשונות</li>
            </ul>
            <h2>האלפבית העברי</h2>
            <p>האלפבית העברי מכיל 22 אותיות:</p>
            <p style="font-size: 1.5em; text-align: center;">א ב ג ד ה ו ז ח ט י כ ל מ נ ס ע פ צ ק ר ש ת</p>
            <h2>תרגילים</h2>
            <p>נסו לקרוא את המילים הבאות:</p>
            <ul>
              <li>שלום</li>
              <li>תודה</li>
              <li>בוקר טוב</li>
            </ul>
          </div>
        `,
        preview_content: "ברוכים הבאים לפרק הראשון בלימוד עברית! בפרק זה נלמד על האלפבית העברי, צלילים בסיסיים ומילים ראשונות...",
        difficulty_level: "beginner",
        order_in_book: 1,
        is_published: true,
        language: "he",
        price: 9.99,
        is_free: true,
        reading_time_minutes: 15,
        tags: ["אלפבית", "בסיסי", "מתחילים"],
        audio_url: null,
        video_url: null,
        created_at: "2025-06-01T10:00:00Z",
        updated_at: "2025-06-15T14:30:00Z",
        created_by: "<EMAIL>",
        last_modified_by: "<EMAIL>",
        stats: {
          views: 2341,
          unique_viewers: 1876,
          purchases: 0,
          completion_rate: 89,
          average_rating: 4.8,
          total_reading_time: 585,
          bounce_rate: 11,
          engagement_score: 92
        },
        seo: {
          meta_title: "Learn Hebrew Alphabet - Chapter 1",
          meta_description: "Introduction to Hebrew alphabet and basic sounds",
          keywords: ["hebrew", "alphabet", "beginner", "learning"]
        }
      },
      "chapter_2": {
        id: "chapter_2",
        title: "פרק שני - מילים בסיסיות",
        content: "Full content for chapter 2...",
        preview_content: "בפרק זה נרחיב את אוצר המילים שלנו ונלמד מילים בסיסיות על משפחה, צבעים ומספרים...",
        difficulty_level: "beginner",
        order_in_book: 2,
        is_published: true,
        language: "he",
        price: 12.99,
        is_free: false,
        reading_time_minutes: 20,
        tags: ["מילים", "משפחה", "צבעים", "מספרים"],
        audio_url: "https://example.com/audio/chapter2.mp3",
        video_url: null,
        created_at: "2025-06-02T10:00:00Z",
        updated_at: "2025-06-16T09:15:00Z",
        created_by: "<EMAIL>",
        last_modified_by: "<EMAIL>",
        stats: {
          views: 1876,
          unique_viewers: 1432,
          purchases: 234,
          completion_rate: 76,
          average_rating: 4.6,
          total_reading_time: 468,
          bounce_rate: 24,
          engagement_score: 78
        },
        seo: {
          meta_title: "Basic Hebrew Vocabulary - Family, Colors, Numbers",
          meta_description: "Learn essential Hebrew words for family, colors, and numbers",
          keywords: ["hebrew", "vocabulary", "family", "colors", "numbers"]
        }
      }
    }

    const chapter = mockChapters[id]
    
    if (!chapter) {
      return res.status(404).json({ message: "Chapter not found" })
    }

    res.json({
      chapter,
      edit_history: [
        {
          timestamp: "2025-06-16T09:15:00Z",
          user: "<EMAIL>",
          action: "Updated content",
          changes: ["Added new exercise section", "Fixed typos"]
        },
        {
          timestamp: "2025-06-15T14:30:00Z", 
          user: "<EMAIL>",
          action: "Published chapter",
          changes: ["Set is_published to true"]
        },
        {
          timestamp: "2025-06-01T10:00:00Z",
          user: "<EMAIL>", 
          action: "Created chapter",
          changes: ["Initial creation"]
        }
      ]
    })
  } catch (error) {
    console.error("Error fetching chapter:", error)
    res.status(500).json({ 
      message: "Internal server error",
      error: error.message
    })
  }
}

// PUT - Update chapter
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params
  
  try {
    const bookService = req.scope.resolve(BOOK_MODULE)
    
    const updateData = req.body
    
    // Validation
    if (updateData.price && updateData.price < 0) {
      return res.status(400).json({
        message: "Price cannot be negative"
      })
    }

    if (updateData.order_in_book && updateData.order_in_book < 1) {
      return res.status(400).json({
        message: "Order in book must be at least 1"
      })
    }

    // Mock update
    const updatedChapter = {
      id,
      ...updateData,
      updated_at: new Date().toISOString(),
      last_modified_by: "<EMAIL>" // In real app, get from auth
    }

    res.json({
      message: "Chapter updated successfully",
      chapter: updatedChapter,
      changes_made: Object.keys(updateData)
    })
  } catch (error) {
    console.error("Error updating chapter:", error)
    res.status(500).json({ 
      message: "Internal server error",
      error: error.message
    })
  }
}

// DELETE - Delete chapter
export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params
  
  try {
    const bookService = req.scope.resolve(BOOK_MODULE)
    
    // In a real app, you might want to soft delete or check for dependencies
    
    res.json({
      message: "Chapter deleted successfully",
      deleted_chapter_id: id,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error("Error deleting chapter:", error)
    res.status(500).json({ 
      message: "Internal server error",
      error: error.message
    })
  }
}
