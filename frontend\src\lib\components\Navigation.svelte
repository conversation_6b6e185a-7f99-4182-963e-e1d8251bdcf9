<script lang="ts">
	import { page } from '$app/stores'
	import { _ } from '$lib/i18n'
	import { getCurrentLocale, isRTL, setLocale } from '$lib/i18n'
	import { goto } from '$app/navigation'
	import CartIcon from './CartIcon.svelte'
	import Cart from './Cart.svelte'

	let currentLocale = 'en'
	let rtl = false
	let mobileMenuOpen = false
	let cartOpen = false

	$: currentLocale = getCurrentLocale()
	$: rtl = isRTL(currentLocale)
	$: currentPath = $page.url.pathname

	function handleLanguageChange(newLocale: string) {
		setLocale(newLocale)
	}

	function toggleMobileMenu() {
		mobileMenuOpen = !mobileMenuOpen
	}

	function closeMobileMenu() {
		mobileMenuOpen = false
	}

	function openCart() {
		cartOpen = true
	}

	function closeCart() {
		cartOpen = false
	}

	function isActive(path: string): boolean {
		return currentPath === path || (path !== '/' && currentPath.startsWith(path))
	}

	const navItems = [
		{ path: '/', label: 'navigation.home' },
		{ path: '/chapters', label: 'navigation.chapters' },
		{ path: '/about', label: 'navigation.about' },
		{ path: '/contact', label: 'navigation.contact' }
	]
</script>

<nav class="bg-white shadow-lg sticky top-0 z-50" class:rtl>
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
		<div class="flex justify-between items-center h-16">
			<!-- Logo -->
			<div class="flex items-center">
				<button 
					on:click={() => goto('/')}
					class="text-xl font-bold text-blue-600 hover:text-blue-800 transition-colors"
				>
					{$_('home.title')}
				</button>
			</div>

			<!-- Desktop Navigation -->
			<div class="hidden md:flex items-center space-x-8" class:space-x-reverse={rtl}>
				{#each navItems as item}
					<a
						href={item.path}
						class="px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
						class:text-blue-600={isActive(item.path)}
						class:bg-blue-50={isActive(item.path)}
						class:text-gray-600={!isActive(item.path)}
						class:hover:text-blue-600={!isActive(item.path)}
						class:hover:bg-gray-50={!isActive(item.path)}
						on:click={closeMobileMenu}
					>
						{$_(item.label)}
					</a>
				{/each}
			</div>

			<!-- Language Switcher, Cart & Mobile Menu Button -->
			<div class="flex items-center space-x-4" class:space-x-reverse={rtl}>
				<!-- Cart Icon -->
				<CartIcon on:open={openCart} />

				<!-- Language Switcher -->
				<div class="flex items-center space-x-2" class:space-x-reverse={rtl}>
					<label for="nav-language-select" class="sr-only">
						{$_('common.language')}
					</label>
					<select
						id="nav-language-select"
						class="block w-auto px-2 py-1 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
						value={currentLocale}
						on:change={(e) => handleLanguageChange((e.target as HTMLSelectElement).value)}
					>
						<option value="en">EN</option>
						<option value="he">עב</option>
						<option value="ru">РУ</option>
					</select>
				</div>

				<!-- Mobile menu button -->
				<button
					type="button"
					class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-600 hover:text-blue-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
					on:click={toggleMobileMenu}
					aria-expanded={mobileMenuOpen}
				>
					<span class="sr-only">Open main menu</span>
					{#if !mobileMenuOpen}
						<svg class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
						</svg>
					{:else}
						<svg class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
						</svg>
					{/if}
				</button>
			</div>
		</div>

		<!-- Mobile Navigation Menu -->
		{#if mobileMenuOpen}
			<div class="md:hidden">
				<div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
					{#each navItems as item}
						<a
							href={item.path}
							class="block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"
							class:text-blue-600={isActive(item.path)}
							class:bg-blue-50={isActive(item.path)}
							class:text-gray-600={!isActive(item.path)}
							class:hover:text-blue-600={!isActive(item.path)}
							class:hover:bg-gray-50={!isActive(item.path)}
							class:text-right={rtl}
							on:click={closeMobileMenu}
						>
							{$_(item.label)}
						</a>
					{/each}


				</div>
			</div>
		{/if}
	</div>
</nav>

<!-- Cart Component -->
<Cart bind:isOpen={cartOpen} on:close={closeCart} />

<style>
	.rtl {
		direction: rtl;
	}
</style>
