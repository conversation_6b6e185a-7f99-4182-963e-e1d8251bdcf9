<script lang="ts">
	import { onMount } from 'svelte'
	import { _ } from '$lib/i18n'
	import { getCurrentLocale, isRTL } from '$lib/i18n'
	import { books, chapterPreviews } from '$lib/data/books'
	import type { Book, ChapterPreview } from '$lib/data/books'

	let currentLocale = 'en'
	let rtl = false
	let selectedBook: Book | null = null
	let selectedChapters: ChapterPreview[] = []
	let expandedChapter: string | null = null

	$: currentLocale = getCurrentLocale()
	$: rtl = isRTL(currentLocale)

	onMount(async () => {
		// Select first book by default
		if (books.length > 0) {
			selectBook(books[0])
		}
	})

	function selectBook(book: Book) {
		selectedBook = book
		selectedChapters = chapterPreviews.filter(chapter => chapter.book_id === book.id)
		expandedChapter = null
	}

	function toggleChapterPreview(chapterId: string) {
		expandedChapter = expandedChapter === chapterId ? null : chapterId
	}

	function formatPrice(price: number): string {
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency: 'USD'
		}).format(price)
	}


</script>

<svelte:head>
	<title>{$_('home.title')} - Hebrew Book Store</title>
	<meta name="description" content={$_('home.description')} />
</svelte:head>

<div class="min-h-screen" style="background: var(--color-bg-primary);" class:rtl>
	<!-- Video Section -->
	<section class="py-8">
		<div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="card-classic p-4" style="background: var(--color-bg-primary);">
				<div class="decorative-border overflow-hidden" style="border-color: var(--color-border);">
					<div class="aspect-w-16 aspect-h-9">
						<iframe
							src="https://www.youtube.com/embed/dQw4w9WgXcQ"
							title="Welcome to Hebrew Learning with Rabbi David Cohen"
							frameborder="0"
							allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
							allowfullscreen
							class="w-full h-full"
							style="aspect-ratio: 16/9; min-height: 400px;"
						></iframe>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Books Section -->
	<section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
		<div class="text-center mb-12">
			<h2 class="font-book-title text-3xl md:text-4xl font-bold mb-4" style="color: var(--color-text-primary);">
				{$_('home.books_title')}
			</h2>
			<h3 class="font-book-title text-lg md:text-xl font-medium mb-6" style="color: var(--color-text-accent);">
				{$_('home.books_subtitle')}
			</h3>
			<p class="font-book-text text-base max-w-3xl mx-auto leading-relaxed" style="color: var(--color-text-secondary);">
				{$_('home.books_description')}
			</p>
		</div>

		<!-- Book Selection Tabs -->
		<div class="flex justify-center mb-12">
			<div class="flex gap-2">
				{#each books as book}
					<button
						class="font-book-text px-6 py-3 text-base font-medium rounded transition-all duration-300 {selectedBook?.id === book.id ? 'btn-classic' : 'hover:bg-opacity-70'}"
						style="{selectedBook?.id === book.id ? '' : `color: var(--color-text-accent); background: var(--color-bg-subtle);`}"
						on:click={() => selectBook(book)}
					>
						{book.title}
					</button>
				{/each}
			</div>
		</div>

		{#if selectedBook}
			<!-- Selected Book Details -->
			<div class="card-classic overflow-hidden mb-16">
				<div class="md:flex">
					<!-- Book Cover -->
					<div class="md:w-1/3 p-8 flex items-center justify-center" style="background: var(--color-bg-subtle);">
						<div class="text-center">
							<div class="decorative-border w-40 h-52 mb-6 flex items-center justify-center" style="background: var(--color-bg-primary); border-color: var(--color-border);">
								<div class="text-center" style="color: var(--color-text-primary);">
									<div class="font-book-title text-4xl mb-2">📖</div>
									<div class="font-book-title text-base font-bold">{selectedBook.title}</div>
								</div>
							</div>
							<h3 class="font-book-title text-xl font-bold mb-2" style="color: var(--color-text-primary);">{selectedBook.title}</h3>
							<p class="font-book-text text-base" style="color: var(--color-text-secondary);">{selectedBook.subtitle}</p>
						</div>
					</div>

					<!-- Book Info -->
					<div class="md:w-2/3 p-8">
						<div class="flex items-center justify-between mb-6">
							<div class="decorative-border px-3 py-1" style="background: var(--color-bg-subtle); border-color: var(--color-border);">
								<span class="font-book-text text-xs font-medium uppercase tracking-wide" style="color: var(--color-text-accent);">
									{$_(`home.difficulty.${selectedBook.difficulty_level}`)}
								</span>
							</div>
							<div class="text-right">
								<div class="font-book-title text-3xl font-bold mb-1" style="color: var(--color-primary);">{formatPrice(selectedBook.price)}</div>
								<div class="font-book-text text-sm" style="color: var(--color-text-light);">{selectedBook.estimated_duration}</div>
							</div>
						</div>

						<p class="font-book-text text-base leading-relaxed mb-8" style="color: var(--color-text-secondary);">{selectedBook.description}</p>

						<div class="grid md:grid-cols-2 gap-6 mb-8">
							<div class="decorative-border p-6" style="background: var(--color-bg-subtle); border-color: var(--color-border);">
								<h4 class="font-book-title text-lg font-bold mb-4" style="color: var(--color-text-primary);">{$_('home.what_you_learn')}</h4>
								<ul class="space-y-2">
									{#each selectedBook.features as feature}
										<li class="flex items-start">
											<div class="w-4 h-4 rounded-full flex items-center justify-center mr-3 mt-1 flex-shrink-0" style="background: var(--color-border-accent);">
												<svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
													<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
												</svg>
											</div>
											<span class="font-book-text text-sm" style="color: var(--color-text-secondary);">{feature}</span>
										</li>
									{/each}
								</ul>
							</div>

							<div class="decorative-border p-6" style="background: var(--color-bg-primary); border-color: var(--color-border);">
								<h4 class="font-book-title text-lg font-bold mb-4" style="color: var(--color-text-primary);">{$_('home.methodology')}</h4>
								<p class="font-book-text text-sm leading-relaxed mb-6" style="color: var(--color-text-secondary);">{selectedBook.methodology}</p>

								<div class="space-y-2">
									<div class="flex items-center font-book-text text-sm" style="color: var(--color-text-accent);">
										<div class="w-3 h-3 rounded-full mr-3" style="background: var(--color-border-accent);"></div>
										{selectedBook.total_chapters} {$_('home.chapters_count')}
									</div>
									<div class="flex items-center font-book-text text-sm" style="color: var(--color-text-accent);">
										<div class="w-3 h-3 rounded-full mr-3" style="background: var(--color-border-accent);"></div>
										{selectedBook.estimated_duration}
									</div>
								</div>
							</div>
						</div>

						<div class="flex flex-col sm:flex-row gap-4">
							<button class="btn-classic flex-1 text-center">
								{$_('home.start_learning')} - {formatPrice(selectedBook.price)}
							</button>
							<button class="decorative-border px-6 py-3 font-book-text font-medium transition-all duration-300 hover:shadow-md" style="background: var(--color-bg-subtle); color: var(--color-text-accent); border-color: var(--color-border);">
								{$_('home.preview_chapters')}
							</button>
						</div>
					</div>
				</div>
			</div>
		{/if}
	</section>

	<!-- Interactive Chapter Table of Contents -->
	{#if selectedBook && selectedChapters.length > 0}
		<section class="py-16" style="background: var(--color-bg-accent);">
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div class="text-center mb-12">
					<h2 class="font-book-title text-3xl md:text-4xl font-bold mb-4" style="color: var(--color-text-primary);">
						{$_('home.chapters_title')}
					</h2>
					<h3 class="font-book-title text-lg md:text-xl font-medium mb-6" style="color: var(--color-text-accent);">
						{$_('home.chapters_subtitle')} - {selectedBook.title}
					</h3>
					<p class="font-book-text text-base leading-relaxed" style="color: var(--color-text-secondary);">
						{$_('home.chapters_description')}
					</p>
				</div>

				<div class="space-y-4">
					{#each selectedChapters as chapter}
						<div class="card-classic overflow-hidden">
							<!-- Chapter Header -->
							<button
								class="w-full px-6 py-4 text-left transition-all duration-300 hover:shadow-md focus:outline-none"
								style="background: var(--color-bg-primary);"
								on:click={() => toggleChapterPreview(chapter.id)}
							>
								<div class="flex items-center justify-between">
									<div class="flex items-center space-x-4">
										<div class="flex-shrink-0">
											<div class="decorative-border w-10 h-10 flex items-center justify-center" style="background: var(--color-border-accent); border-color: var(--color-border);">
												<span class="font-book-title text-sm font-bold text-white">
													{chapter.order}
												</span>
											</div>
										</div>
										<div>
											<h3 class="font-book-title text-lg font-bold mb-1" style="color: var(--color-text-primary);">{chapter.title}</h3>
											<p class="font-book-text text-sm" style="color: var(--color-text-secondary);">{chapter.description}</p>
										</div>
									</div>
									<div class="flex items-center space-x-4">
										<div class="text-right">
											<div class="flex items-center space-x-2 mb-1">
												<div class="decorative-border px-2 py-1" style="background: var(--color-bg-subtle); border-color: var(--color-border);">
													<span class="font-book-text text-xs font-medium uppercase tracking-wide" style="color: var(--color-text-accent);">
														{$_(`home.difficulty.${chapter.difficulty}`)}
													</span>
												</div>
												{#if chapter.is_free}
													<div class="decorative-border px-2 py-1" style="background: var(--color-border-accent); border-color: var(--color-border);">
														<span class="font-book-text text-xs font-medium uppercase tracking-wide text-white">
															{$_('home.free')}
														</span>
													</div>
												{/if}
											</div>
											<div class="font-book-text text-xs" style="color: var(--color-text-light);">{chapter.duration}</div>
										</div>
										<div class="w-6 h-6 rounded-full flex items-center justify-center transition-all duration-300 {expandedChapter === chapter.id ? 'rotate-180' : ''}" style="background: var(--color-border-accent);">
											<svg
												class="w-3 h-3 text-white"
												fill="none"
												stroke="currentColor"
												viewBox="0 0 24 24"
											>
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
											</svg>
										</div>
									</div>
								</div>
							</button>

							<!-- Chapter Preview Content -->
							{#if expandedChapter === chapter.id}
								<div class="px-6 pb-6" style="border-top: 1px solid var(--color-border-light);">
									<div class="pt-4">
										<h4 class="font-book-title text-base font-bold mb-3" style="color: var(--color-text-primary);">{$_('home.chapter_preview')}</h4>
										<div class="decorative-border p-4 mb-4" style="background: var(--color-bg-subtle); border-color: var(--color-border);">
											<p class="font-book-text text-sm leading-relaxed" style="color: var(--color-text-secondary);">{chapter.preview_content}</p>
										</div>

										<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
											<div class="flex flex-wrap items-center gap-3 font-book-text text-xs" style="color: var(--color-text-accent);">
												<span class="flex items-center">
													<span class="w-2 h-2 rounded-full mr-2" style="background: var(--color-border-accent);"></span>
													{$_('home.chapter')} {chapter.order}
												</span>
												<span class="flex items-center">
													<span class="w-2 h-2 rounded-full mr-2" style="background: var(--color-border-accent);"></span>
													{chapter.duration}
												</span>
												<span class="flex items-center">
													<span class="w-2 h-2 rounded-full mr-2" style="background: var(--color-border-accent);"></span>
													{$_(`home.difficulty.${chapter.difficulty}`)}
												</span>
											</div>

											<div class="flex gap-2">
												{#if chapter.is_free}
													<button class="btn-classic text-sm px-4 py-2">
														{$_('home.start_free_chapter')}
													</button>
												{:else}
													<button class="decorative-border px-4 py-2 font-book-text text-sm font-medium transition-all duration-300 hover:shadow-md" style="background: var(--color-bg-subtle); color: var(--color-text-accent); border-color: var(--color-border);">
														{$_('home.preview')}
													</button>
													<button class="btn-classic text-sm px-4 py-2">
														{$_('home.unlock_chapter')}
													</button>
												{/if}
											</div>
										</div>
									</div>
								</div>
							{/if}
						</div>
					{/each}
				</div>

				<!-- Call to Action -->
				<div class="text-center mt-12">
					<div class="card-classic p-8 max-w-2xl mx-auto">
						<h3 class="font-book-title text-2xl font-bold mb-2" style="color: var(--color-text-primary);">{$_('home.cta_hebrew')}</h3>
						<h4 class="font-book-title text-xl font-medium mb-4" style="color: var(--color-text-accent);">{$_('home.cta_title')}</h4>
						<p class="font-book-text text-base leading-relaxed mb-6 max-w-xl mx-auto" style="color: var(--color-text-secondary);">
							{$_('home.cta_description')}
						</p>
						<div class="flex flex-col sm:flex-row gap-4 justify-center">
							<button class="btn-classic px-8 py-3">
								{$_('home.start_learning')} - {selectedBook ? formatPrice(selectedBook.price) : ''}
							</button>
							<button class="decorative-border px-8 py-3 font-book-text font-medium transition-all duration-300 hover:shadow-md" style="background: var(--color-bg-subtle); color: var(--color-text-accent); border-color: var(--color-border);">
								{$_('home.try_free')}
							</button>
						</div>
					</div>
				</div>
			</div>
		</section>
	{/if}
</div>
