import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { BOOK_MODULE } from "../../../modules/book"

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const bookService = req.scope.resolve(BOOK_MODULE)

    // Test service availability
    res.json({
      message: "Hebrew Book Store API is working!",
      service_available: !!bookService,
      service_type: typeof bookService,
      service_methods: Object.getOwnPropertyNames(Object.getPrototypeOf(bookService)),
      timestamp: new Date().toISOString(),
      modules_loaded: Object.keys(req.scope.cradle).filter(key => key.includes('Module') || key.includes('Service'))
    })
  } catch (error) {
    console.error("Test error:", error)
    res.status(500).json({
      message: "Test failed",
      error: error.message,
      stack: error.stack
    })
  }
}
