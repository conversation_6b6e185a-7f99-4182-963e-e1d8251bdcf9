# ⚡ Quick Test Guide - Hebrew Book Store

## 🚀 Start Server
```bash
yarn dev
# Server: http://localhost:9000
```

## 🎯 Test Links (Click to Open)

### 🌐 **Main Demo (Multilingual)**
**[http://localhost:9000/demo-v2](http://localhost:9000/demo-v2)**
- Test language switching: 🇺🇸 EN | 🇮🇱 עב | 🇷🇺 РУ
- Try Hebrew search: "משפחה"
- Load chapters and subscriptions

### 🛠️ **Admin Dashboard**
**[http://localhost:9000/admin-demo](http://localhost:9000/admin-demo)**
- Switch languages in admin interface
- View analytics and metrics
- Test chapter management

### 📊 **Classic Demo**
**[http://localhost:9000/demo](http://localhost:9000/demo)**
- Interactive API testing
- Hebrew content display
- Search functionality

## 📡 Quick API Tests

```bash
# Health check
curl http://localhost:9000/health

# Get chapters
curl http://localhost:9000/public/chapters

# Search Hebrew content
curl "http://localhost:9000/public/search?q=משפחה"

# Get translations
curl "http://localhost:9000/public/translations?language=he"

# Subscription plans
curl http://localhost:9000/public/subscriptions/plans
```

## ✅ What to Test

1. **Language Switching** - Click language buttons, see RTL for Hebrew
2. **Hebrew Content** - Verify Hebrew text displays correctly
3. **Search** - Try Hebrew, English, Russian search terms
4. **Responsive Design** - Test on different screen sizes
5. **API Responses** - Check all endpoints return data

## 🎉 Success Indicators

- ✅ All pages load without errors
- ✅ Language switching works smoothly
- ✅ Hebrew displays right-to-left
- ✅ Search returns relevant results
- ✅ APIs return JSON data
- ✅ Admin dashboard shows metrics

**🌟 Platform is fully functional and ready for use! 🌟**
