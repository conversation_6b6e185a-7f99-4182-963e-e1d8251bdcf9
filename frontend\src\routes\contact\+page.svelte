<script lang="ts">
	import { _ } from '$lib/i18n'
	import { getCurrentLocale, isRTL } from '$lib/i18n'

	let currentLocale = 'en'
	let rtl = false
	let formData = {
		name: '',
		email: '',
		subject: '',
		message: ''
	}
	let isSubmitting = false
	let submitMessage = ''
	let submitError = ''

	$: currentLocale = getCurrentLocale()
	$: rtl = isRTL(currentLocale)

	async function handleSubmit(event: Event) {
		event.preventDefault()
		isSubmitting = true
		submitMessage = ''
		submitError = ''

		try {
			// Simulate form submission
			await new Promise(resolve => setTimeout(resolve, 1000))
			
			// In a real app, you would send this to your backend
			console.log('Contact form submitted:', formData)
			
			submitMessage = 'Thank you for your message! We will get back to you soon.'
			formData = { name: '', email: '', subject: '', message: '' }
		} catch (error) {
			submitError = 'There was an error sending your message. Please try again.'
		} finally {
			isSubmitting = false
		}
	}
</script>

<svelte:head>
	<title>{$_('navigation.contact')} - Hebrew Book Store</title>
	<meta name="description" content="Get in touch with us for support or questions about Hebrew learning" />
</svelte:head>

<div class="min-h-screen bg-gray-50" class:rtl>
	<!-- Page Header -->
	<div class="bg-white border-b border-gray-200">
		<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
			<h1 class="text-4xl font-bold text-gray-900 mb-4" class:text-right={rtl}>
				{$_('navigation.contact')}
			</h1>
			<p class="text-xl text-gray-600" class:text-right={rtl}>
				We're here to help with your Hebrew learning journey
			</p>
		</div>
	</div>

	<!-- Main Content -->
	<main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
			<!-- Contact Information -->
			<div>
				<div class="bg-white rounded-lg shadow-md p-8">
					<h2 class="text-2xl font-bold text-gray-900 mb-6" class:text-right={rtl}>
						Get in Touch
					</h2>
					
					<div class="space-y-6">
						<div class="flex items-start space-x-4" class:space-x-reverse={rtl}>
							<div class="flex-shrink-0">
								<div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
									<svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
									</svg>
								</div>
							</div>
							<div>
								<h3 class="text-lg font-semibold text-gray-900 mb-1" class:text-right={rtl}>
									Email Support
								</h3>
								<p class="text-gray-600" class:text-right={rtl}>
									Send us an email and we'll respond within 24 hours
								</p>
								<a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-800">
									<EMAIL>
								</a>
							</div>
						</div>

						<div class="flex items-start space-x-4" class:space-x-reverse={rtl}>
							<div class="flex-shrink-0">
								<div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
									<svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 24 24">
										<path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 2.079.549 4.03 1.506 5.709L0 24l6.573-1.487c1.64.855 3.491 1.357 5.444 1.357 6.621 0 11.988-5.367 11.988-11.987C23.976 5.367 18.609.001 12.017.001zM12.017 21.986c-1.896 0-3.67-.498-5.212-1.357l-.374-.222-3.873.877.905-3.646-.246-.39C2.411 15.944 1.93 14.027 1.93 11.987c0-5.569 4.526-10.095 10.087-10.095 2.692 0 5.22 1.05 7.115 2.945 1.895 1.895 2.945 4.423 2.945 7.115 0 5.569-4.526 10.034-10.06 10.034zm5.503-7.515c-.301-.151-1.783-.88-2.059-.98-.276-.101-.477-.151-.678.151-.201.301-.779.98-.955 1.181-.176.201-.352.226-.653.075-.301-.151-1.271-.469-2.421-1.494-.894-.799-1.497-1.785-1.673-2.086-.176-.301-.019-.464.132-.614.135-.135.301-.352.452-.528.151-.176.201-.301.301-.502.101-.201.05-.377-.025-.528-.075-.151-.678-1.634-.93-2.237-.246-.588-.497-.508-.678-.518-.176-.009-.377-.009-.578-.009-.201 0-.528.075-.804.377-.276.301-1.054 1.030-1.054 2.513s1.080 2.915 1.230 3.116c.151.201 2.132 3.257 5.166 4.567.722.311 1.286.497 1.725.636.725.230 1.386.198 1.909.120.582-.087 1.783-.729 2.034-1.432.251-.703.251-1.306.176-1.432-.075-.125-.276-.201-.577-.352z"/>
									</svg>
								</div>
							</div>
							<div>
								<h3 class="text-lg font-semibold text-gray-900 mb-1" class:text-right={rtl}>
									WhatsApp Support
								</h3>
								<p class="text-gray-600" class:text-right={rtl}>
									Chat with us directly for quick questions
								</p>
								<a href="https://wa.me/1234567890" target="_blank" rel="noopener noreferrer" class="text-green-600 hover:text-green-800">
									Start WhatsApp Chat
								</a>
							</div>
						</div>

						<div class="flex items-start space-x-4" class:space-x-reverse={rtl}>
							<div class="flex-shrink-0">
								<div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
									<svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
									</svg>
								</div>
							</div>
							<div>
								<h3 class="text-lg font-semibold text-gray-900 mb-1" class:text-right={rtl}>
									Response Time
								</h3>
								<p class="text-gray-600" class:text-right={rtl}>
									We typically respond within 2-4 hours during business hours
								</p>
								<p class="text-sm text-gray-500" class:text-right={rtl}>
									Sunday - Thursday: 9:00 AM - 6:00 PM (Israel Time)
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Contact Form -->
			<div>
				<div class="bg-white rounded-lg shadow-md p-8">
					<h2 class="text-2xl font-bold text-gray-900 mb-6" class:text-right={rtl}>
						Send us a Message
					</h2>

					{#if submitMessage}
						<div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
							<p class="text-green-800" class:text-right={rtl}>{submitMessage}</p>
						</div>
					{/if}

					{#if submitError}
						<div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
							<p class="text-red-800" class:text-right={rtl}>{submitError}</p>
						</div>
					{/if}

					<form on:submit={handleSubmit} class="space-y-6">
						<div>
							<label for="name" class="block text-sm font-medium text-gray-700 mb-2" class:text-right={rtl}>
								Full Name *
							</label>
							<input
								type="text"
								id="name"
								bind:value={formData.name}
								required
								class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
								class:text-right={rtl}
								placeholder="Enter your full name"
							/>
						</div>

						<div>
							<label for="email" class="block text-sm font-medium text-gray-700 mb-2" class:text-right={rtl}>
								Email Address *
							</label>
							<input
								type="email"
								id="email"
								bind:value={formData.email}
								required
								class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
								class:text-right={rtl}
								placeholder="Enter your email address"
							/>
						</div>

						<div>
							<label for="subject" class="block text-sm font-medium text-gray-700 mb-2" class:text-right={rtl}>
								Subject *
							</label>
							<select
								id="subject"
								bind:value={formData.subject}
								required
								class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
								class:text-right={rtl}
							>
								<option value="">Select a subject</option>
								<option value="technical-support">Technical Support</option>
								<option value="content-question">Content Question</option>
								<option value="billing">Billing & Payment</option>
								<option value="feedback">Feedback & Suggestions</option>
								<option value="other">Other</option>
							</select>
						</div>

						<div>
							<label for="message" class="block text-sm font-medium text-gray-700 mb-2" class:text-right={rtl}>
								Message *
							</label>
							<textarea
								id="message"
								bind:value={formData.message}
								required
								rows="5"
								class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
								class:text-right={rtl}
								placeholder="Tell us how we can help you..."
							></textarea>
						</div>

						<div>
							<button
								type="submit"
								disabled={isSubmitting}
								class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
							>
								{#if isSubmitting}
									<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
										<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
										<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
									</svg>
									Sending...
								{:else}
									Send Message
								{/if}
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</main>
</div>

<style>
	.rtl {
		direction: rtl;
	}
</style>
