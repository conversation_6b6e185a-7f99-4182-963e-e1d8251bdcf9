import { NextFunction, Request, Response } from "express"

export function corsMiddleware(req: Request, res: Response, next: NextFunction) {
  // Allow all origins in development
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Credentials', 'true')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-publishable-api-key, x-medusa-access-token, Cookie')
  res.setHeader('Access-Control-Expose-Headers', 'set-cookie')

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }

  next()
}
