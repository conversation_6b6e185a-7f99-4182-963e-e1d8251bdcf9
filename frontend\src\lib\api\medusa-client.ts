import { browser } from '$app/environment'

const BACKEND_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:9000'
const PUBLISHABLE_API_KEY = import.meta.env.VITE_MEDUSA_PUBLISHABLE_API_KEY

// Debug: Log environment variables (only in development)
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  console.log('🔧 Simple Medusa Client Configuration:')
  console.log('  - Backend URL:', BACKEND_URL)
  console.log('  - Publishable Key:', PUBLISHABLE_API_KEY ? `${PUBLISHABLE_API_KEY.substring(0, 10)}...` : 'NOT SET')
}

class SimpleMedusaClient {
  private baseUrl: string
  private publishableKey: string
  private authToken: string | null = null

  constructor() {
    this.baseUrl = BACKEND_URL
    this.publishableKey = PUBLISHABLE_API_KEY || ''

    // Load auth token from localStorage
    if (browser) {
      this.authToken = localStorage.getItem('medusa_auth_token')
    }
  }

  private getHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    if (this.publishableKey) {
      headers['x-publishable-api-key'] = this.publishableKey
    }

    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`
    }

    return headers
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`
    const config: RequestInit = {
      ...options,
      headers: {
        ...this.getHeaders(),
        ...options.headers,
      },
    }

    const response = await fetch(url, config)
    const data = await response.json()

    if (!response.ok) {
      throw new Error(data.message || data.error || `HTTP ${response.status}`)
    }

    return data
  }

  // Auth methods
  async register(email: string, password: string): Promise<string> {
    const data = await this.request<{ token: string }>('/store/auth/customer/emailpass/register', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    })

    this.setAuthToken(data.token)
    return data.token
  }

  async login(email: string, password: string): Promise<string> {
    const data = await this.request<{ token: string }>('/store/auth/customer/emailpass', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    })

    this.setAuthToken(data.token)
    return data.token
  }

  async logout(): Promise<void> {
    try {
      await this.request('/store/auth/customer/emailpass', {
        method: 'DELETE',
      })
    } catch (error) {
      console.warn('Logout request failed:', error)
    }

    this.clearAuthToken()
  }

  // Customer methods
  async createCustomer(customerData: {
    email: string
    first_name: string
    last_name: string
    phone?: string
  }): Promise<any> {
    const data = await this.request<{ customer: any }>('/store/customers', {
      method: 'POST',
      body: JSON.stringify(customerData),
    })

    return data.customer
  }

  async getCustomer(): Promise<any> {
    const data = await this.request<{ customer: any }>('/store/customers/me')
    return data.customer
  }

  // Token management
  setAuthToken(token: string): void {
    this.authToken = token
    if (browser) {
      localStorage.setItem('medusa_auth_token', token)
    }
  }

  clearAuthToken(): void {
    this.authToken = null
    if (browser) {
      localStorage.removeItem('medusa_auth_token')
    }
  }

  getAuthToken(): string | null {
    return this.authToken
  }
}

export const medusaClient = new SimpleMedusaClient()
export default medusaClient
