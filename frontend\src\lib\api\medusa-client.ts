import Medusa from "@medusajs/js-sdk"
import { browser } from '$app/environment'

const BACKEND_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:9000'
const PUBLISHABLE_API_KEY = import.meta.env.VITE_MEDUSA_PUBLISHABLE_API_KEY

// Debug: Log environment variables (only in development)
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  console.log('🔧 Medusa SDK Configuration:')
  console.log('  - Backend URL:', BACKEND_URL)
  console.log('  - Publishable Key:', PUBLISHABLE_API_KEY ? `${PUBLISHABLE_API_KEY.substring(0, 10)}...` : 'NOT SET')
}

// Create Medusa SDK instance
export const medusaClient = new Medusa({
  baseUrl: BACKEND_URL,
  publishableKey: PUBLISHABLE_API_KEY,
  auth: {
    type: "jwt" // Use JWT tokens instead of session cookies
  }
})

// Debug: Log SDK initialization
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  console.log('✅ Medusa SDK initialized')
}

export default medusaClient
