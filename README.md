<p align="center">
  <a href="https://www.medusajs.com">
  <picture>
    <source media="(prefers-color-scheme: dark)" srcset="https://user-images.githubusercontent.com/59018053/229103275-b5e482bb-4601-46e6-8142-244f531cebdb.svg">
    <source media="(prefers-color-scheme: light)" srcset="https://user-images.githubusercontent.com/59018053/229103726-e5b529a3-9b3f-4970-8a1f-c6af37f087bf.svg">
    <img alt="Medusa logo" src="https://user-images.githubusercontent.com/59018053/229103726-e5b529a3-9b3f-4970-8a1f-c6af37f087bf.svg">
    </picture>
  </a>
</p>
<h1 align="center">
  Medusa
</h1>

<h4 align="center">
  <a href="https://docs.medusajs.com">Documentation</a> |
  <a href="https://www.medusajs.com">Website</a>
</h4>

<p align="center">
  Building blocks for digital commerce
</p>
<p align="center">
  <a href="https://github.com/medusajs/medusa/blob/master/CONTRIBUTING.md">
    <img src="https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat" alt="PRs welcome!" />
  </a>
    <a href="https://www.producthunt.com/posts/medusa"><img src="https://img.shields.io/badge/Product%20Hunt-%231%20Product%20of%20the%20Day-%23DA552E" alt="Product Hunt"></a>
  <a href="https://discord.gg/xpCwq3Kfn8">
    <img src="https://img.shields.io/badge/chat-on%20discord-7289DA.svg" alt="Discord Chat" />
  </a>
  <a href="https://twitter.com/intent/follow?screen_name=medusajs">
    <img src="https://img.shields.io/twitter/follow/medusajs.svg?label=Follow%20@medusajs" alt="Follow @medusajs" />
  </a>
</p>

## Compatibility

This starter is compatible with versions >= 2 of `@medusajs/medusa`. 

## Getting Started

Visit the [Quickstart Guide](https://docs.medusajs.com/learn/installation) to set up a server.

Visit the [Docs](https://docs.medusajs.com/learn/installation#get-started) to learn more about our system requirements.

## What is Medusa

Medusa is a set of commerce modules and tools that allow you to build rich, reliable, and performant commerce applications without reinventing core commerce logic. The modules can be customized and used to build advanced ecommerce stores, marketplaces, or any product that needs foundational commerce primitives. All modules are open-source and freely available on npm.

Learn more about [Medusa’s architecture](https://docs.medusajs.com/learn/introduction/architecture) and [commerce modules](https://docs.medusajs.com/learn/fundamentals/modules/commerce-modules) in the Docs.

# 🇮🇱 Hebrew Book Store - E-commerce Platform

**Advanced Hebrew Learning Platform** built with Medusa.js v2

This project demonstrates a complete e-commerce platform for Hebrew language learning with digital content protection, subscription system, multilingual support, and payment integration.

## 🚀 Quick Start

### **Backend (Medusa.js)**
1. **Install dependencies:**
   ```bash
   yarn install
   ```

2. **Start the backend server:**
   ```bash
   yarn dev
   ```

3. **Backend runs on:** `http://localhost:9000`

### **Frontend (SvelteKit)**
1. **Navigate to frontend:**
   ```bash
   cd frontend
   ```

2. **Start the frontend server:**
   ```bash
   # Recommended method
   npx vite dev --port 5173

   # Or use startup script (Windows)
   start-frontend.bat

   # Or use startup script (Linux/Mac)
   ./start-frontend.sh
   ```

3. **Frontend runs on:** `http://localhost:5173`

> **Note:** Don't use `yarn run dev` in the frontend directory as it may conflict with the backend. Use `npx vite dev --port 5173` instead.

### **Full Stack Development**
Run both servers simultaneously for complete development experience:
- **Backend:** http://localhost:9000 (API, Admin, Demos)
- **Frontend:** http://localhost:5173 (Main Application)

## 🌐 Live Demo Pages

### 🎯 **Main Application (SvelteKit Frontend)**
**URL:** [http://localhost:5173](http://localhost:5173)

**Features:**
- ✅ **Complete Hebrew Book Store** - Full application experience
- ✅ **SvelteKit + TypeScript** - Modern frontend framework
- ✅ **Tailwind CSS** - Beautiful, responsive design
- ✅ **Multilingual Support** - Hebrew (RTL), English, Russian
- ✅ **Chapter Reading** - Enhanced content display
- ✅ **Search Functionality** - Hebrew content search
- ✅ **User Authentication** - Login and registration
- ✅ **Progress Tracking** - Reading progress management

### 🎯 **Backend Demo (Multilingual)**
**URL:** [http://localhost:9000/demo-v2](http://localhost:9000/demo-v2)

**Features:**
- ✅ **Language Switcher** - 🇺🇸 English | 🇮🇱 Hebrew | 🇷🇺 Russian
- ✅ **RTL Support** - Proper Hebrew text direction
- ✅ **Interactive Search** - Hebrew content search
- ✅ **Chapter Management** - Browse learning content
- ✅ **Subscription Plans** - Pricing and features
- ✅ **Real-time Analytics** - Platform statistics

### 🛠️ **Admin Dashboard**
**URL:** [http://localhost:9000/admin-demo](http://localhost:9000/admin-demo)

**Features:**
- ✅ **Multilingual Admin Interface**
- ✅ **Chapter Management** - CRUD operations
- ✅ **Analytics Dashboard** - Revenue, users, engagement
- ✅ **Content Performance** - Views, completion rates
- ✅ **User Statistics** - Geographic distribution

### 📊 **Classic Demo**
**URL:** [http://localhost:9000/demo](http://localhost:9000/demo)

**Features:**
- ✅ **API Testing Interface** - Interactive endpoint testing
- ✅ **Hebrew Content Display** - Sample chapters
- ✅ **Search Functionality** - Content discovery
- ✅ **Platform Statistics** - Usage metrics

## 📡 API Endpoints

### Public APIs
```bash
# Get all chapters with multilingual support
curl http://localhost:9000/public/chapters

# Search Hebrew content
curl "http://localhost:9000/public/search?q=משפחה"

# Get subscription plans
curl http://localhost:9000/public/subscriptions/plans

# Get translations for specific language
curl "http://localhost:9000/public/translations?language=he"

# Platform statistics
curl http://localhost:9000/public/stats
```

### Admin APIs
```bash
# Admin chapter management
curl http://localhost:9000/admin/chapters

# Comprehensive analytics
curl http://localhost:9000/admin/analytics

# Specific chapter details
curl http://localhost:9000/admin/chapters/chapter_1
```

## 🎯 Key Features Implemented

### ✅ **Multilingual Support**
- **3 Languages:** English (primary), Hebrew (RTL), Russian
- **Real-time switching** without page reload
- **API-driven translations** system
- **Cultural adaptation** for different markets

### ✅ **Hebrew Content Management**
- **RTL text direction** support
- **Hebrew typography** optimization
- **Content access control** (free vs paid)
- **Reading progress tracking**

### ✅ **E-commerce Features**
- **Individual chapter purchases** ($9.99 - $18.99)
- **Subscription plans** (6 months: $29.99, 12 months: $49.99)
- **Stripe payment integration** with webhooks
- **Access control** based on purchases/subscriptions

### ✅ **Analytics & Admin**
- **Real-time dashboard** with metrics
- **Revenue tracking** and reporting
- **User engagement** analytics
- **Content performance** monitoring

## 🔧 Technical Stack

### **Frontend**
- **Framework:** SvelteKit with TypeScript
- **Styling:** Tailwind CSS with RTL support
- **Build Tool:** Vite with proxy configuration
- **Package Manager:** Yarn (aligned with backend)
- **Features:** i18n, authentication, responsive design

### **Backend**
- **Framework:** Medusa.js v2 with TypeScript
- **Database:** PostgreSQL with proper migrations
- **Payment:** Stripe integration with webhooks
- **Email:** SendGrid for notifications
- **Languages:** English, Hebrew (RTL), Russian
- **Architecture:** Modular design with custom modules

### **Integration**
- **API Proxy:** Vite development proxy
- **Communication:** RESTful APIs with JSON
- **Authentication:** JWT tokens
- **Real-time:** WebSocket support ready

## 📚 Documentation

### 🎯 **Main Documentation**
- **[Complete Project Documentation](HEBREW_BOOK_STORE.md)** - Full feature overview
- **[Frontend Integration Guide](FRONTEND_INTEGRATION.md)** - SvelteKit integration details
- **[Multilingual Implementation](MULTILINGUAL_UPDATE.md)** - Language support details
- **[Testing Guide](TESTING_GUIDE.md)** - Comprehensive testing instructions
- **[Deployment Guide](DEPLOYMENT_GUIDE.md)** - Production deployment instructions
- **[Quick Test Guide](QUICK_TEST.md)** - Fast testing checklist
- **[Project Summary](PROJECT_SUMMARY.md)** - Technical achievements

### 🔐 **Admin Panel (Medusa.js Official)**
- **URL**: `http://localhost:9000/app` (local) or `http://your-domain.com:9000/app` (production)
- **Note**: Uses only official Medusa.js admin panel. Custom Svelte admin panel has been removed.
- **[Admin Auth Setup Guide](docs/ADMIN_AUTH_SETUP.md)** - Detailed invite-based authentication setup

### 👤 **Admin User Creation**
```bash
# Local development
cd backend
yarn medusa user -e <EMAIL> -p admin123

# Production
yarn medusa user -e <EMAIL> -p your-secure-password
```

### 🔑 **API Key Setup**
1. **Login to admin panel**: `http://localhost:9000/app`
2. **Go to**: Settings → API Keys → Create API Key
3. **Select type**: Publishable
4. **Copy the key** and add to frontend environment:
```bash
# frontend/.env.local
VITE_MEDUSA_PUBLISHABLE_API_KEY=pk_your_actual_key_here
```
- **[Quick Admin Setup](docs/QUICK_ADMIN_SETUP.md)** - Fast admin user creation (2 minutes)

> **⚠️ Important**: Medusa.js v2 uses invite-based authentication for admin users. Don't try to create users directly with password - use the invite system!

## 🎉 Project Status: **COMPLETED** ✅

This is a **fully functional e-commerce platform** ready for production use with:
- ✅ Complete backend implementation
- ✅ Working multilingual interface
- ✅ Hebrew content with RTL support
- ✅ Payment and subscription system
- ✅ Admin dashboard with analytics
- ✅ Interactive demos

---

## Community & Contributions

The community and core team are available in [GitHub Discussions](https://github.com/medusajs/medusa/discussions), where you can ask for support, discuss roadmap, and share ideas.

Join our [Discord server](https://discord.com/invite/medusajs) to meet other community members.

## Other channels

- [GitHub Issues](https://github.com/medusajs/medusa/issues)
- [Twitter](https://twitter.com/medusajs)
- [LinkedIn](https://www.linkedin.com/company/medusajs)
- [Medusa Blog](https://medusajs.com/blog/)
