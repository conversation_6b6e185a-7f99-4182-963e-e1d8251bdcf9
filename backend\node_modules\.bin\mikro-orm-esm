#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -x "$basedir/-S" ]; then
  "$basedir/-S"  node --loader ts-node/esm --no-warnings=ExperimentalWarning "$basedir/../../../node_modules/@mikro-orm/cli/esm" "$@"
  ret=$?
else 
  -S  node --loader ts-node/esm --no-warnings=ExperimentalWarning "$basedir/../../../node_modules/@mikro-orm/cli/esm" "$@"
  ret=$?
fi
exit $ret
